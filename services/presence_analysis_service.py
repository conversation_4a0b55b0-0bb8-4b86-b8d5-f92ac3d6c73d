import json
from typing import Dict
from utils.prompts import GptPromptPicker
from utils.logger import get_logger
from utils.gemini_client import GeminiClient
from utils.json_parser import parse_json_response
from api.time_func import timeit

logger = get_logger(__name__)
gemini_client = GeminiClient()

@timeit
async def analyze_website_presence(website_url: str) -> Dict:
    """
    Analyzes the online presence of a website.

    Args:
        website_url: The URL of the website to analyze.

    Returns:
        A dictionary containing the analysis results.
    """
    try:
        # Validate input
        if not website_url:
            logger.error("Empty website URL provided")
            return {"analyzed_url": website_url, "error": "Empty website URL provided"}
        
        # Clean and validate URL
        website_url = str(website_url).strip()
        if not website_url.startswith(('http://', 'https://')):
            website_url = 'https://' + website_url
        
        logger.info(f"Starting presence analysis for URL: {website_url}")
        
        # Generate prompt
        try:
            prompt = GptPromptPicker.get_website_presence_prompt(website_url=website_url)
            logger.info(f"Generated prompt for {website_url} (length: {len(prompt)} characters)")
            if len(prompt) > 500:
                logger.debug(f"Prompt preview for {website_url}: {prompt[:500]}...")
        except Exception as prompt_error:
            logger.error(f"Failed to generate prompt for {website_url}: {prompt_error}")
            return {"analyzed_url": website_url, "error": f"Failed to generate prompt: {str(prompt_error)}"}

        # Make API request
        try:
            response_text = await gemini_client.make_request_async(
                prompt, 
                url=website_url, 
                use_url_context=False, 
                use_grounding=True
            )
        except Exception as api_error:
            logger.error(f"API request failed for {website_url}: {api_error}")
            return {"analyzed_url": website_url, "error": f"API request failed: {str(api_error)}"}

        if not response_text:
            logger.warning(f"No response received for URL: {website_url}")
            return {"analyzed_url": website_url, "error": "No response from API"}

        logger.info(f"Received presence analysis response for {website_url} (length: {len(response_text)} characters)")
        logger.info(f"=== PRESENCE ANALYSIS RESPONSE FOR {website_url} ===")
        logger.info(f"Raw response: {response_text}")
        logger.info(f"=== END PRESENCE ANALYSIS RESPONSE ===")

        # Parse response
        try:
            response_json = parse_json_response(response_text, "presence analysis")
            if not response_json:
                logger.warning(f"Empty JSON response for {website_url}")
                return {"analyzed_url": website_url, "error": "Empty JSON response"}
            
            response_json["analyzed_url"] = website_url
            logger.info(f"Successfully parsed presence analysis for {website_url}")
            logger.info(f"Parsed JSON keys: {list(response_json.keys())}")
            return response_json
        except Exception as parse_error:
            logger.error(f"Failed to parse presence analysis for {website_url}: {parse_error}")
            return {
                "analyzed_url": website_url,
                "error": "Failed to parse response",
                "raw_response": response_text
            }
            
    except Exception as e:
        logger.error(f"Unexpected error in presence analysis for {website_url}: {e}", exc_info=True)
        return {
            "analyzed_url": website_url,
            "error": f"Unexpected error: {str(e)}"
        }
from typing import Dict, List
from utils.prompts import <PERSON><PERSON><PERSON><PERSON>pt<PERSON><PERSON>
from utils.logger import get_logger
from utils.gemini_client import GeminiClient
from utils.json_parser import parse_json_response
from api.time_func import timeit

logger = get_logger(__name__)

# Gemini URL context tool has a limit of 20 URLs per request
GEMINI_URL_CONTEXT_LIMIT = 20
# Conservative batch size to avoid hitting the limit - reduced to 10 to stay well under 20
DEFAULT_BATCH_SIZE = 10

# Initialize Gemini client lazily to avoid import-time errors
_gemini_client = None

def get_gemini_client():
    """Get or create the Gemini client instance."""
    global _gemini_client
    if _gemini_client is None:
        _gemini_client = GeminiClient()
    return _gemini_client

def extract_reachable_urls_from_metadata(batch_dict: Dict[int, str]) -> List[int]:
    """
    Extract reachable URLs from the last Gemini response's URL context metadata.
    This is a backup method to cross-reference with the JSON response.

    Args:
        batch_dict: The batch dictionary that was sent to Gemini.

    Returns:
        A list of integer keys corresponding to URLs that were successfully retrieved.
    """
    # Note: This would require access to the response metadata from the last request
    # For now, we'll rely on the JSON response from the prompt
    # This could be enhanced to access the actual metadata if needed
    return []

def check_batch_reachability_with_retry(batch_dict: Dict[int, str], max_retries: int = 3) -> List[int]:
    """
    Checks URL reachability using Gemini URL context tool with retry logic for batch size reduction.

    Args:
        batch_dict: A dictionary of URLs to check, with integer keys.
        max_retries: Maximum number of retries with smaller batches.

    Returns:
        A list of integer keys corresponding to reachable URLs.
    """
    try:
        # Validate input
        if not batch_dict:
            logger.warning("Empty batch dictionary provided")
            return []
        
        original_batch_size = len(batch_dict)
        # Start with a conservative batch size to avoid hitting the 20 URL limit
        # Gemini's URL context tool has a limit of 20 URLs, so we start with DEFAULT_BATCH_SIZE to be safe
        current_batch_size = min(original_batch_size, DEFAULT_BATCH_SIZE)

        for attempt in range(max_retries + 1):
            try:
                # If we need to reduce batch size, take only the first N URLs
                if current_batch_size < original_batch_size:
                    batch_indices = list(batch_dict.keys())[:current_batch_size]
                    current_batch = {idx: batch_dict[idx] for idx in batch_indices}
                    logger.info(f"Processing batch of {len(current_batch)} URLs (attempt {attempt + 1}/{max_retries + 1})")
                else:
                    current_batch = batch_dict
                    logger.info(f"Processing batch of {len(current_batch)} URLs (attempt {attempt + 1}/{max_retries + 1})")

                # Generate prompt for current batch
                try:
                    prompt = GptPromptPicker.get_website_reachability_prompt(current_batch)
                    logger.info(f"Generated reachability prompt (length: {len(prompt)} characters)")
                    if len(prompt) > 500:
                        logger.debug(f"Prompt preview: {prompt[:500]}...")
                except Exception as prompt_error:
                    logger.error(f"Failed to generate prompt for batch: {prompt_error}")
                    return []

                # Make request with URL context enabled
                try:
                    response_text = get_gemini_client().make_request(prompt, use_url_context=True, use_grounding=False)
                except Exception as api_error:
                    logger.error(f"API request failed for batch: {api_error}")
                    if attempt < max_retries:
                        continue
                    return []

                if response_text:
                    logger.info(f"Received reachability response: {response_text}")
                    logger.info(f"=== REACHABILITY RESPONSE ===")
                    logger.info(f"Raw response: {response_text}")
                    logger.info(f"=== END REACHABILITY RESPONSE ===")

                    # Parse JSON response to get reachable URLs
                    try:
                        response_json = parse_json_response(response_text, "reachability check")

                        if response_json:
                            reachable_indices = response_json.get("reachable_urls", [])
                            logger.info(f"Found {len(reachable_indices)} reachable URLs in this batch: {reachable_indices}")
                            logger.info(f"Parsed JSON response: {response_json}")
                            return reachable_indices
                        else:
                            logger.warning(f"Failed to parse JSON response for batch: {list(current_batch.keys())}")
                            logger.warning(f"Raw response that failed to parse: {response_text}")
                            return []
                    except Exception as parse_error:
                        logger.error(f"Failed to parse response for batch: {parse_error}")
                        return []
                else:
                    logger.warning(f"No response received for batch: {list(current_batch.keys())}")
                    return []

            except Exception as e:
                error_message = str(e)
                logger.error(f"Error in batch reachability check (attempt {attempt + 1}): {error_message}")

                # Check if it's the URL limit error
                if "Number of urls to lookup exceeds the limit" in error_message and attempt < max_retries:
                    # Reduce batch size more aggressively - cut it in half, minimum 1
                    current_batch_size = max(1, current_batch_size // 2)
                    logger.info(f"URL limit exceeded, reducing batch size to {current_batch_size}")
                    continue
                else:
                    logger.error(f"Failed after {attempt + 1} attempts")
                    return []

        # If we get here, all attempts failed
        logger.error(f"All {max_retries + 1} attempts failed for batch")
        return []
        
    except Exception as e:
        logger.error(f"Unexpected error in batch reachability check: {e}", exc_info=True)
        return []


@timeit
def check_url_reachability(url_dict: Dict[int, str], target_reachable_count: int = 4) -> List[str]:
    """
    Checks the reachability of URLs using Gemini URL context tool and returns a list of reachable URLs.

    Args:
        url_dict: A dictionary of URLs to check, with integer keys.
        target_reachable_count: The number of reachable URLs to find before stopping.

    Returns:
        A list of reachable URLs.
    """
    try:
        # Validate input
        if not url_dict:
            logger.warning("Empty URL dictionary provided")
            return []
        
        reachable_urls = []
        checked_indices = set()
        sorted_indices = sorted(url_dict.keys())

        logger.info(f"Starting reachability check for {len(url_dict)} URLs using Gemini URL context tool")

        # Process URLs in batches of GEMINI_URL_CONTEXT_LIMIT, with retry logic for smaller batches
        # Using GEMINI_URL_CONTEXT_LIMIT as the maximum to stay within Gemini's URL context tool limits
        batch_size = GEMINI_URL_CONTEXT_LIMIT
        for i in range(0, len(sorted_indices), batch_size):
            if len(reachable_urls) >= target_reachable_count:
                logger.info(f"Target of {target_reachable_count} reachable URLs reached. Stopping.")
                break

            batch_indices = sorted_indices[i:i + batch_size]
            batch_dict = {index: url_dict[index] for index in batch_indices if index not in checked_indices}

            if not batch_dict:
                continue

            # Use retry logic with batch size reduction
            reachable_indices = check_batch_reachability_with_retry(batch_dict, max_retries=3)

            # Add reachable URLs to the result list
            for index in reachable_indices:
                if len(reachable_urls) < target_reachable_count and index in url_dict:
                    reachable_url = url_dict[index]
                    reachable_urls.append(reachable_url)
                    logger.info(f"Added reachable URL: {reachable_url}")

            # Mark these indices as checked
            checked_indices.update(batch_dict.keys())

        # Return results after processing all batches
        logger.info(f"Found a total of {len(reachable_urls)} reachable URLs.")
        return reachable_urls
            
    except Exception as e:
        logger.error(f"Unexpected error in reachability check: {e}", exc_info=True)
        return []
import json
import re
import async<PERSON>
from typing import List, Dict
from utils.prompts import GptPromptPicker
from utils.logger import get_logger
from utils.gemini_client import Gemini<PERSON>lient
from api.time_func import timeit

logger = get_logger(__name__)
gemini_client = GeminiClient()

def clean_json_response(response_text: str) -> str:
    """
    Clean JSON response by removing markdown code blocks and other formatting.
    
    Args:
        response_text: Raw response text from the API
        
    Returns:
        Cleaned JSON string
    """
    cleaned_response = response_text.strip()
    
    # Remove markdown code blocks
    if cleaned_response.startswith("```json"):
        cleaned_response = cleaned_response[7:]
    elif cleaned_response.startswith("```"):
        cleaned_response = cleaned_response[3:]
        
    if cleaned_response.endswith("```"):
        cleaned_response = cleaned_response[:-3]
    
    # Remove any leading/trailing whitespace
    cleaned_response = cleaned_response.strip()
    
    # Try to extract JSON from text if it's embedded
    json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
    if json_match:
        cleaned_response = json_match.group(0)
    
    return cleaned_response

async def analyze_single_url(url: str) -> Dict:
    """Analyzes a single URL asynchronously."""
    try:
        # Validate input
        if not url:
            logger.error("Empty URL provided")
            return {"analyzed_url": url, "error": "Empty URL provided"}
        
        # Clean and validate URL
        url = str(url).strip()
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        logger.info(f"Starting health analysis for URL: {url}")
        
        # Generate prompt
        try:
            prompt = GptPromptPicker.get_website_health_analysis_prompt(website_url=url)
            logger.info(f"Generated prompt for {url} (length: {len(prompt)} characters)")
            if len(prompt) > 500:
                logger.debug(f"Prompt preview for {url}: {prompt[:500]}...")
        except Exception as prompt_error:
            logger.error(f"Failed to generate prompt for {url}: {prompt_error}")
            return {"analyzed_url": url, "error": f"Failed to generate prompt: {str(prompt_error)}"}
        
        # Make API request
        try:
            response_text = await gemini_client.make_request_async(prompt, url=url, use_url_context=True)
        except Exception as api_error:
            logger.error(f"API request failed for {url}: {api_error}")
            return {"analyzed_url": url, "error": f"API request failed: {str(api_error)}"}

        if not response_text:
            logger.warning(f"No response received for URL: {url}")
            return {"analyzed_url": url, "error": "No response from API"}

        logger.info(f"Received health analysis response for {url} (length: {len(response_text)} characters)")
        logger.info(f"=== HEALTH ANALYSIS RESPONSE FOR {url} ===")
        logger.info(f"Raw response: {response_text}")
        logger.info(f"=== END HEALTH ANALYSIS RESPONSE ===")
        
        # Parse response
        try:
            cleaned_response = clean_json_response(response_text)
            logger.info(f"Cleaned response for {url}: {cleaned_response}")
            
            if not cleaned_response:
                logger.warning(f"Empty cleaned response for {url}")
                return {"analyzed_url": url, "error": "Empty cleaned response"}
            
            response_json = json.loads(cleaned_response)
            response_json["analyzed_url"] = url
            logger.info(f"Successfully parsed health analysis for {url}")
            logger.info(f"Parsed JSON keys: {list(response_json.keys())}")
            return response_json
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON for {url}: {e}")
            logger.error(f"Cleaned response that failed to parse: {cleaned_response}")
            return {
                "analyzed_url": url,
                "error": "Failed to parse response",
                "raw_response": response_text
            }
        except Exception as parse_error:
            logger.error(f"Failed to parse health analysis for {url}: {parse_error}")
            return {
                "analyzed_url": url,
                "error": "Failed to parse response",
                "raw_response": response_text
            }
            
    except Exception as e:
        logger.error(f"Unexpected error in health analysis for {url}: {e}", exc_info=True)
        return {
            "analyzed_url": url,
            "error": f"Unexpected error: {str(e)}"
        }

@timeit
async def analyze_website_health(reachable_urls: List[str], home_page_url: str) -> List[dict]:
    """
    Analyzes the health of a list of URLs concurrently and returns the analysis results.
    """
    final_urls = sorted(list(set(reachable_urls + [home_page_url])))
    
    tasks = [analyze_single_url(url) for url in final_urls]
    
    analysis_results = await asyncio.gather(*tasks)
    
    return analysis_results
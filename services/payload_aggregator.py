"""
Website Health Payload Aggregator Service

This module provides functionality to aggregate multiple website health analysis results
into a single consolidated payload following specific business rules.
"""

from typing import List, Dict, Any, Union
from utils.logger import get_logger

logger = get_logger(__name__)


def aggregate_website_health_payload(website_health_payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Aggregates website health analysis results from multiple URLs into a single summary.
    
    Business Rules:
    - Boolean fields: If ANY result has "yes", the final result is "yes"
    - List fields: Combine all lists from all results
    - Reason fields: Convert strings to lists and combine all values
    
    Args:
        website_health_payload: Dictionary containing website health analysis results
                               with a 'results' key containing list of URL analyses
    
    Returns:
        Dict containing aggregated results in the specified format
    """
    try:
        logger.info("Starting website health payload aggregation")
        
        # Extract results from payload
        results = website_health_payload.get("results", [])
        
        if not results:
            logger.warning("No results found in website health payload")
            return _get_empty_aggregated_payload()
        
        logger.info(f"Aggregating {len(results)} URL analysis results")
        
        # Initialize aggregated payload structure
        aggregated = {
            "navigation_issues_exists": "no",
            "navigation_issue_type": [],
            "navigation_issues_area": [],
            "redirection_same_page": "no",
            "links": [],
            "phishing_site": "no",
            "phishing_reason": [],
            "malware_present": "no",
            "malware_reason": [],
            "security_review_present": "no",
            "security_review": [],
            "security_review_source": [],
            "analyzed_url": []
        }
        
        # Process each URL result
        for i, result in enumerate(results):
            logger.debug(f"Processing result {i+1}/{len(results)} for URL: {result.get('analyzed_url', 'unknown')}")
            
            # Boolean aggregation: if any "yes" then overall "yes"
            if result.get("navigation_issues_exists") == "yes":
                aggregated["navigation_issues_exists"] = "yes"
            
            if result.get("redirection_same_page") == "yes":
                aggregated["redirection_same_page"] = "yes"
            
            if result.get("phishing_site") == "yes":
                aggregated["phishing_site"] = "yes"
            
            if result.get("malware_present") == "yes":
                aggregated["malware_present"] = "yes"
            
            if result.get("security_review_present") == "yes":
                aggregated["security_review_present"] = "yes"
            
            # List aggregation: combine all lists
            _extend_list_field(aggregated, result, "navigation_issue_type")
            _extend_list_field(aggregated, result, "navigation_issues_area")
            _extend_list_field(aggregated, result, "links")
            _extend_list_field(aggregated, result, "security_review_source")
            
            # URL aggregation
            analyzed_url = result.get("analyzed_url")
            if analyzed_url:
                aggregated["analyzed_url"].append(analyzed_url)
            
            # Reason fields: convert to list and combine
            _extend_reason_field(aggregated, result, "phishing_reason")
            _extend_reason_field(aggregated, result, "malware_reason")
            _extend_reason_field(aggregated, result, "security_review")
        
        # Remove duplicates from lists while preserving order
        for field in ["navigation_issue_type", "navigation_issues_area", "links", 
                     "phishing_reason", "malware_reason", "security_review", 
                     "security_review_source", "analyzed_url"]:
            aggregated[field] = _remove_duplicates_preserve_order(aggregated[field])
        
        logger.info("Successfully aggregated website health payload")
        logger.debug(f"Aggregated payload summary: "
                    f"navigation_issues={aggregated['navigation_issues_exists']}, "
                    f"phishing={aggregated['phishing_site']}, "
                    f"malware={aggregated['malware_present']}, "
                    f"total_urls={len(aggregated['analyzed_url'])}")
        
        return aggregated
        
    except Exception as e:
        logger.error(f"Error aggregating website health payload: {e}", exc_info=True)
        raise


def _extend_list_field(aggregated: Dict[str, Any], result: Dict[str, Any], field_name: str) -> None:
    """
    Extends a list field in the aggregated payload with values from a single result.
    
    Args:
        aggregated: The aggregated payload being built
        result: Single URL analysis result
        field_name: Name of the field to extend
    """
    field_value = result.get(field_name, [])
    if isinstance(field_value, list):
        aggregated[field_name].extend(field_value)
    elif field_value:  # Handle non-empty strings or other values
        aggregated[field_name].append(str(field_value))


def _extend_reason_field(aggregated: Dict[str, Any], result: Dict[str, Any], field_name: str) -> None:
    """
    Extends a reason field in the aggregated payload, converting strings to lists.
    
    Args:
        aggregated: The aggregated payload being built
        result: Single URL analysis result
        field_name: Name of the reason field to extend
    """
    field_value = result.get(field_name)
    
    if isinstance(field_value, list):
        aggregated[field_name].extend(field_value)
    elif isinstance(field_value, str) and field_value.strip():
        # Convert non-empty string to list
        aggregated[field_name].append(field_value.strip())
    # Skip empty strings, None, or other falsy values


def _remove_duplicates_preserve_order(items: List[Any]) -> List[Any]:
    """
    Removes duplicates from a list while preserving the original order.
    
    Args:
        items: List that may contain duplicates
        
    Returns:
        List with duplicates removed, order preserved
    """
    seen = set()
    result = []
    for item in items:
        if item not in seen:
            seen.add(item)
            result.append(item)
    return result


def _get_empty_aggregated_payload() -> Dict[str, Any]:
    """
    Returns an empty aggregated payload structure.
    
    Returns:
        Dict with all fields initialized to default values
    """
    return {
        "navigation_issues_exists": "no",
        "navigation_issue_type": [],
        "navigation_issues_area": [],
        "redirection_same_page": "no",
        "links": [],
        "phishing_site": "no",
        "phishing_reason": [],
        "malware_present": "no",
        "malware_reason": [],
        "security_review_present": "no",
        "security_review": [],
        "security_review_source": [],
        "analyzed_url": []
    }


# Example usage and testing function
def test_aggregation_with_sample_data():
    """
    Test function using the sample data from cmd.txt
    """
    sample_payload = {
        "website": "https://ecoop.in",
        "scrapeRequestUuid": "104094a3-2ec4-4c41-8473-9b9d1bed2268",
        "createdDate": "2025-07-18T17:01:02.306745Z",
        "status": "COMPLETED",
        "results": [
            {
                "navigation_issues_exists": "no",
                "navigation_issue_type": [],
                "navigation_issues_area": [],
                "redirection_same_page": "no",
                "links": [],
                "phishing_site": "no",
                "phishing_reason": "Cannot assess phishing indicators or site reputation as a search/grounding tool is unavailable.",
                "malware_present": "no",
                "malware_reason": "Cannot investigate malware presence or security blacklists as a search/grounding tool is unavailable.",
                "security_review_present": "no",
                "security_review": "Cannot search for security reviews or scam reports as a search/grounding tool is unavailable.",
                "security_review_source": [],
                "analyzed_url": "https://ecoop.in"
            },
            {
                "navigation_issues_exists": "yes",
                "navigation_issue_type": [
                    "Disabled Links",
                    "Redirection to same page"
                ],
                "navigation_issues_area": [
                    "Privacy policy (footer)",
                    "Terms and Conditions (footer)",
                    "Shipping Policy (footer)",
                    "Refund Policy (footer)",
                    "Cookies Policy (footer)"
                ],
                "redirection_same_page": "yes",
                "links": [
                    "#"
                ],
                "phishing_site": "no",
                "phishing_reason": "N/A",
                "malware_present": "no",
                "malware_reason": "N/A",
                "security_review_present": "no",
                "security_review": "N/A",
                "security_review_source": [],
                "analyzed_url": "https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health"
            }
        ]
    }
    
    result = aggregate_website_health_payload(sample_payload)
    print("Aggregated Result:")
    import json
    print(json.dumps(result, indent=2))
    return result


if __name__ == "__main__":
    test_aggregation_with_sample_data()

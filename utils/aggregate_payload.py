#!/usr/bin/env python3
"""
Standalone utility for aggregating website health payloads.

This script can be used to aggregate existing website health analysis results
without needing to run the full API.
"""

import json
import sys
import os
from typing import Dict, Any

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.payload_aggregator import aggregate_website_health_payload


def aggregate_from_file(input_file: str, output_file: str = None) -> Dict[str, Any]:
    """
    Aggregate website health payload from a JSON file.
    
    Args:
        input_file: Path to JSON file containing website health payload
        output_file: Optional path to save aggregated result
        
    Returns:
        Aggregated payload dictionary
    """
    try:
        # Read input file
        with open(input_file, 'r') as f:
            payload = json.load(f)
        
        print(f"Loaded payload from {input_file}")
        print(f"Found {len(payload.get('results', []))} URL analysis results")
        
        # Aggregate
        aggregated = aggregate_website_health_payload(payload)
        
        print("Aggregation completed successfully")
        
        # Save to output file if specified
        if output_file:
            with open(output_file, 'w') as f:
                json.dump(aggregated, f, indent=2)
            print(f"Aggregated result saved to {output_file}")
        
        return aggregated
        
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input file: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def aggregate_from_dict(payload_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Aggregate website health payload from a dictionary.
    
    Args:
        payload_dict: Dictionary containing website health payload
        
    Returns:
        Aggregated payload dictionary
    """
    return aggregate_website_health_payload(payload_dict)


def print_summary(aggregated: Dict[str, Any]) -> None:
    """Print a human-readable summary of the aggregated results."""
    
    print("\n" + "="*50)
    print("AGGREGATED WEBSITE HEALTH SUMMARY")
    print("="*50)
    
    # Security summary
    print("\n🔒 SECURITY ASSESSMENT:")
    print(f"  Phishing Risk:     {aggregated.get('phishing_site', 'N/A').upper()}")
    print(f"  Malware Risk:      {aggregated.get('malware_present', 'N/A').upper()}")
    print(f"  Security Reviews:  {aggregated.get('security_review_present', 'N/A').upper()}")
    
    # Navigation summary
    print("\n🧭 NAVIGATION ASSESSMENT:")
    print(f"  Navigation Issues: {aggregated.get('navigation_issues_exists', 'N/A').upper()}")
    print(f"  Redirection Issues: {aggregated.get('redirection_same_page', 'N/A').upper()}")
    
    # Issue details
    nav_types = aggregated.get('navigation_issue_type', [])
    if nav_types:
        print(f"  Issue Types: {', '.join(nav_types)}")
    
    nav_areas = aggregated.get('navigation_issues_area', [])
    if nav_areas:
        print(f"  Affected Areas: {', '.join(nav_areas[:3])}{'...' if len(nav_areas) > 3 else ''}")
    
    # URL summary
    urls = aggregated.get('analyzed_url', [])
    print(f"\n📊 ANALYSIS SCOPE:")
    print(f"  Total URLs Analyzed: {len(urls)}")
    if urls:
        print(f"  Primary Domain: {urls[0] if urls else 'N/A'}")
    
    # Reason summaries
    phishing_reasons = aggregated.get('phishing_reason', [])
    if phishing_reasons:
        print(f"\n🎣 PHISHING ASSESSMENT DETAILS:")
        for i, reason in enumerate(phishing_reasons[:2], 1):
            print(f"  {i}. {reason[:80]}{'...' if len(reason) > 80 else ''}")
        if len(phishing_reasons) > 2:
            print(f"  ... and {len(phishing_reasons) - 2} more reasons")
    
    malware_reasons = aggregated.get('malware_reason', [])
    if malware_reasons:
        print(f"\n🦠 MALWARE ASSESSMENT DETAILS:")
        for i, reason in enumerate(malware_reasons[:2], 1):
            print(f"  {i}. {reason[:80]}{'...' if len(reason) > 80 else ''}")
        if len(malware_reasons) > 2:
            print(f"  ... and {len(malware_reasons) - 2} more reasons")
    
    print("\n" + "="*50)


def main():
    """Main CLI interface."""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python3 aggregate_payload.py <input_file.json> [output_file.json]")
        print("  python3 aggregate_payload.py --help")
        print("\nExample:")
        print("  python3 aggregate_payload.py website_health_results.json aggregated_summary.json")
        sys.exit(1)
    
    if sys.argv[1] in ['--help', '-h']:
        print("Website Health Payload Aggregator")
        print("=================================")
        print("\nThis utility aggregates multiple URL analysis results into a single summary.")
        print("\nUsage:")
        print("  python3 aggregate_payload.py <input_file.json> [output_file.json]")
        print("\nArguments:")
        print("  input_file.json   - JSON file containing website health analysis results")
        print("  output_file.json  - Optional output file for aggregated results")
        print("\nThe input file should contain a 'results' array with individual URL analyses.")
        print("The aggregated output follows the same structure but consolidates all findings.")
        sys.exit(0)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Perform aggregation
    aggregated = aggregate_from_file(input_file, output_file)
    
    # Print summary
    print_summary(aggregated)
    
    # Print JSON if no output file specified
    if not output_file:
        print("\nFull aggregated JSON:")
        print(json.dumps(aggregated, indent=2))


if __name__ == "__main__":
    main()

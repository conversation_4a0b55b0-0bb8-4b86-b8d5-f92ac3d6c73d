import os
import time
import async<PERSON>
from google import genai
from google.genai.types import <PERSON><PERSON>, GenerateContentConfig, UrlContext, GoogleSearch
from utils.logger import get_logger

logger = get_logger(__name__)

class GeminiClient:
    def __init__(self, api_key: str = None):
        try:
            # Configure API key
            if api_key:
                self.api_key = api_key
            else:
                # If no API key is provided, configure it through environment variables
                self.api_key = os.getenv('GEMINI_API_KEY')
                if not self.api_key:
                    logger.warning("No Gemini API key provided. Please set GEMINI_API_KEY environment variable.")

            # Initialize the client with the new Google AI SDK only if we have an API key
            if self.api_key:
                try:
                    self.client = genai.Client(api_key=self.api_key)
                    self.model_id = "gemini-2.5-flash"  # Use Gemini 2.5 Flash model
                    
                    # Create URL context tool for web analysis
                    self.url_context_tool = Tool(
                        url_context=UrlContext()
                    )
                    
                    # Create Google search tool for web research capabilities
                    self.google_search_tool = Tool(
                        google_search=GoogleSearch()
                    )
                    
                    logger.info("Gemini client initialized successfully")
                except Exception as init_error:
                    logger.error(f"Failed to initialize Gemini client: {init_error}")
                    self.client = None
                    self.model_id = None
                    self.url_context_tool = None
                    self.google_search_tool = None
            else:
                self.client = None
                self.model_id = None
                self.url_context_tool = None
                self.google_search_tool = None
                logger.warning("Gemini client not initialized due to missing API key")
        except Exception as e:
            logger.error(f"Error during Gemini client initialization: {e}")
            self.client = None
            self.model_id = None
            self.url_context_tool = None
            self.google_search_tool = None
        
    async def make_request_async(self, prompt: str, url: str = None, use_url_context: bool = True, use_grounding: bool = False, max_retries: int = 3) -> str:
        """
        Makes an asynchronous request to the Gemini API with the given prompt.
        Note: The new Google AI SDK doesn't support async methods, so this falls back to sync.
        """
        if not self.client:
            logger.error("Gemini client not initialized. Please provide a valid API key.")
            return None
            
        logger.info(f"Async request initiated for URL: {url}, URL context: {use_url_context}, Grounding: {use_grounding}")
        logger.info("Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool")
        
        # Since the new SDK doesn't support async, we'll use the sync method
        # but run it in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, 
            self.make_request, 
            prompt, 
            url, 
            use_url_context, 
            use_grounding,
            max_retries
        )
        
        logger.info(f"Async request completed for URL: {url}, result length: {len(result) if result else 0}")
        return result

    def make_request(self, prompt: str, url: str = None, use_url_context: bool = True, use_grounding: bool = False, max_retries: int = 3) -> str:
        """
        Makes a request to the Gemini API with the given prompt using the new SDK.

        Args:
            prompt: The prompt to send to the language model.
            url: Optional URL for context when analyzing specific websites.
            use_url_context: Whether to enable URL context tool (default: True).
            use_grounding: Whether to enable grounding tool for Google search (default: False).
            max_retries: Maximum number of retry attempts (default: 3).

        Returns:
            The response from the language model as a string.
        """
        if not self.client:
            logger.error("Gemini client not initialized. Please provide a valid API key.")
            return None
            
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"Making request to Gemini API (attempt {attempt + 1}/{max_retries + 1})...")
                if url:
                    logger.info(f"Including URL context: {url}")

                # Configure the request with appropriate tools
                tools = []
                if use_url_context:
                    tools.append(self.url_context_tool)
                if use_grounding:
                    tools.append(self.google_search_tool)
                
                config = GenerateContentConfig(
                    tools=tools,
                    response_modalities=["TEXT"],
                )

                # Log the request details
                logger.info(f"Request details - Model: {self.model_id}, URL context: {use_url_context}, Grounding: {use_grounding}, Tools count: {len(tools)}")
                logger.debug(f"Prompt length: {len(prompt)} characters")
                if len(prompt) > 500:
                    logger.debug(f"Prompt preview: {prompt[:500]}...")
                else:
                    logger.debug(f"Full prompt: {prompt}")

                # Generate content with the new SDK
                response = self.client.models.generate_content(
                    model=self.model_id,
                    contents=prompt,
                    config=config
                )

                # Log the raw response object for debugging
                logger.debug(f"Raw response object type: {type(response)}")
                logger.debug(f"Raw response attributes: {dir(response)}")

                if response and response.candidates and len(response.candidates) > 0:
                    # Extract text from response parts
                    response_text = ""
                    for part in response.candidates[0].content.parts:
                        if hasattr(part, 'text') and part.text:
                            response_text += part.text

                    if response_text:
                        logger.info(f"Received response from Gemini API (length: {len(response_text)} characters)")
                        
                        # Log the full response content
                        logger.info(f"=== GEMINI RESPONSE CONTENT ===")
                        logger.info(f"Response text: {response_text}")
                        logger.info(f"=== END GEMINI RESPONSE ===")
                        
                        # Log response metadata
                        logger.info(f"Response metadata - Candidates count: {len(response.candidates)}")
                        logger.info(f"First candidate finish reason: {getattr(response.candidates[0], 'finish_reason', 'N/A')}")
                        
                        # Log usage metadata if available
                        if hasattr(response, 'usage_metadata'):
                            usage = response.usage_metadata
                            logger.info(f"Usage metadata - Prompt token count: {getattr(usage, 'prompt_token_count', 'N/A')}")
                            logger.info(f"Usage metadata - Candidates token count: {getattr(usage, 'candidates_token_count', 'N/A')}")
                            logger.info(f"Usage metadata - Total token count: {getattr(usage, 'total_token_count', 'N/A')}")

                        # Log URL context metadata if available
                        if hasattr(response.candidates[0], 'url_context_metadata') and response.candidates[0].url_context_metadata:
                            url_metadata = response.candidates[0].url_context_metadata
                            logger.info(f"=== URL CONTEXT METADATA ===")
                            logger.info(f"URL context metadata: {url_metadata}")
                            
                            if hasattr(url_metadata, 'url_metadata') and url_metadata.url_metadata:
                                successful_urls = []
                                failed_urls = []
                                for metadata in url_metadata.url_metadata:
                                    if hasattr(metadata, 'url_retrieval_status'):
                                        if 'SUCCESS' in str(metadata.url_retrieval_status):
                                            successful_urls.append(metadata.retrieved_url)
                                        else:
                                            failed_urls.append(metadata.retrieved_url)
                                
                                logger.info(f"URL retrieval summary - Successful: {len(successful_urls)}, Failed: {len(failed_urls)}")
                                if failed_urls:
                                    logger.warning(f"Failed URLs: {failed_urls}")
                                if successful_urls:
                                    logger.info(f"Successful URLs: {successful_urls}")
                            logger.info(f"=== END URL CONTEXT METADATA ===")
                        else:
                            logger.info("No URL context metadata available in response")

                        # Log safety ratings if available
                        if hasattr(response.candidates[0], 'safety_ratings'):
                            safety_ratings = response.candidates[0].safety_ratings
                            if safety_ratings:
                                logger.info(f"=== SAFETY RATINGS ===")
                                for rating in safety_ratings:
                                    logger.info(f"Safety rating - Category: {getattr(rating, 'category', 'N/A')}, Probability: {getattr(rating, 'probability', 'N/A')}")
                                logger.info(f"=== END SAFETY RATINGS ===")
                            else:
                                logger.info("No safety ratings in response")

                        return response_text
                    else:
                        logger.warning("Received empty response text from Gemini API")
                        logger.warning(f"Response candidates: {response.candidates}")
                        if attempt < max_retries:
                            logger.info(f"Retrying in 2 seconds...")
                            time.sleep(2)
                            continue
                        return None
                else:
                    logger.warning("Received empty response from Gemini API")
                    logger.warning(f"Response object: {response}")
                    if attempt < max_retries:
                        logger.info(f"Retrying in 2 seconds...")
                        time.sleep(2)
                        continue
                    return None

            except Exception as e:
                logger.error(f"An error occurred while making a request to the Gemini API (attempt {attempt + 1}): {e}", exc_info=True)
                
                # Log additional error details
                logger.error(f"Error type: {type(e).__name__}")
                logger.error(f"Error message: {str(e)}")
                
                if attempt < max_retries:
                    logger.info(f"Retrying in 2 seconds...")
                    time.sleep(2)
                    continue
                else:
                    logger.error(f"All {max_retries + 1} attempts failed")
                    return None

        return None

2025-08-02 17:22:14,113 - __main__ - INFO - [-] - [-] - Starting Website Health Analysis API on 0.0.0.0:8000
2025-08-02 17:22:14,113 - __main__ - INFO - [-] - [-] - Reload mode: True
INFO:     Will watch for changes in these directories: ['/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [9000] using StatReload
2025-08-02 17:22:15,700 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
2025-08-02 17:22:15,716 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
INFO:     Started server process [9003]
INFO:     Waiting for application startup.
2025-08-02 17:22:15,742 - database.config - INFO - [-] - [-] - Database tables created successfully
2025-08-02 17:22:15,742 - api.main - INFO - [-] - [-] - Database initialized successfully
INFO:     Application startup complete.
INFO:     127.0.0.1:50202 - "GET / HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50212 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50202 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:50202 - "GET /openapi.json HTTP/1.1" 200 OK
2025-08-02 17:22:47,833 - api.time_func - INFO - [-] - [-] - Function analyze_health starting
2025-08-02 17:22:47,833 - api.main - INFO - [-] - [-] - Received request for website: https://ecoop.in
2025-08-02 17:22:47,833 - api.main - INFO - [-] - [-] - Start time: **********.8331537
2025-08-02 17:22:47,833 - api.main - INFO - [-] - [-] - Step 1: Processing URLs...
2025-08-02 17:22:47,833 - utils.url_processor - INFO - [-] - [-] - Processing URLs from 1 depth items
2025-08-02 17:22:47,833 - utils.url_processor - INFO - [-] - [-] - Total URLs extracted: 88
2025-08-02 17:22:47,834 - utils.url_processor - INFO - [-] - [-] - Valid URLs after filtering: 88
2025-08-02 17:22:47,834 - utils.url_processor - INFO - [-] - [-] - Unique URLs after deduplication: 88
2025-08-02 17:22:47,834 - utils.url_processor - INFO - [-] - [-] - URLs after sorting by length and taking top 100: 88
2025-08-02 17:22:47,834 - api.main - INFO - [-] - [-] - Processed 88 unique URLs from 88 total URLs
2025-08-02 17:22:47,834 - api.main - INFO - [-] - [-] - Step 2: Checking URL reachability...
2025-08-02 17:22:47,834 - api.time_func - INFO - [-] - [-] - Function check_url_reachability starting
2025-08-02 17:22:47,834 - services.reachability_service - INFO - [-] - [-] - Starting reachability check for 88 URLs using Gemini URL context tool
2025-08-02 17:22:47,834 - services.reachability_service - INFO - [-] - [-] - Processing batch of 10 URLs (attempt 1/4)
2025-08-02 17:22:47,834 - services.reachability_service - INFO - [-] - [-] - Generated reachability prompt (length: 1850 characters)
2025-08-02 17:22:47,852 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
2025-08-02 17:22:47,852 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:22:47,852 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 19 characters)
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - Response text: {"reachable_urls":}
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 480
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 164
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 803
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/search?q=Cap',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/search?q=NEO',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/pages/contact',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/search?q=Mocha',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/collections/all',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/search?q=Schoon',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/search?q=Alkarich',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-02 17:22:59,194 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 4, Failed: 6
2025-08-02 17:22:59,194 - utils.gemini_client - WARNING - [-] - [-] - Failed URLs: ['https://ecoop.in/cdn', 'https://ecoop.in/search?q=Cap', 'https://ecoop.in/search?q=NEO', 'https://ecoop.in/search?q=Mocha', 'https://ecoop.in/search?q=Schoon', 'https://ecoop.in/search?q=Alkarich']
2025-08-02 17:22:59,195 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in', 'https://ecoop.in/blogs/news', 'https://ecoop.in/pages/contact', 'https://ecoop.in/collections/all']
2025-08-02 17:22:59,195 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:22:59,195 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:22:59,195 - services.reachability_service - INFO - [-] - [-] - Received reachability response: {"reachable_urls":}
2025-08-02 17:22:59,195 - services.reachability_service - INFO - [-] - [-] - === REACHABILITY RESPONSE ===
2025-08-02 17:22:59,195 - services.reachability_service - INFO - [-] - [-] - Raw response: {"reachable_urls":}
2025-08-02 17:22:59,195 - services.reachability_service - INFO - [-] - [-] - === END REACHABILITY RESPONSE ===
2025-08-02 17:22:59,195 - utils.json_parser - ERROR - [-] - [-] - Failed to decode JSON from API response for reachability check: Expecting value: line 1 column 19 (char 18)
2025-08-02 17:22:59,195 - utils.json_parser - ERROR - [-] - [-] - Raw response: {"reachable_urls":}
2025-08-02 17:22:59,195 - utils.json_parser - ERROR - [-] - [-] - Cleaned response: {"reachable_urls":}
2025-08-02 17:22:59,195 - services.reachability_service - WARNING - [-] - [-] - Failed to parse JSON response for batch: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
2025-08-02 17:22:59,195 - services.reachability_service - WARNING - [-] - [-] - Raw response that failed to parse: {"reachable_urls":}
2025-08-02 17:22:59,195 - services.reachability_service - INFO - [-] - [-] - Processing batch of 10 URLs (attempt 1/4)
2025-08-02 17:22:59,195 - services.reachability_service - INFO - [-] - [-] - Generated reachability prompt (length: 2132 characters)
2025-08-02 17:22:59,195 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:22:59,195 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 19 characters)
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Response text: {"reachable_urls":}
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 630
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 304
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 1306
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/pages/return-and-refund-policy',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/collections/regular-water-bottles',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/collections/premium-coffee-tumbler',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/collections/protection-case-covers',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/collections/replaceable-cartridges',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/1.png?v=1745642560&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/2.png?v=1745642562&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/3.png?v=1745642563&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/1.png?v=1745642560&width=1127',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/2.png?v=1745642562&width=1127',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 4, Failed: 6
2025-08-02 17:23:08,645 - utils.gemini_client - WARNING - [-] - [-] - Failed URLs: ['https://ecoop.in/collections/replaceable-cartridges', 'https://ecoop.in/cdn/shop/files/1.png?v=1745642560&width=600', 'https://ecoop.in/cdn/shop/files/2.png?v=1745642562&width=600', 'https://ecoop.in/cdn/shop/files/3.png?v=1745642563&width=600', 'https://ecoop.in/cdn/shop/files/1.png?v=1745642560&width=1127', 'https://ecoop.in/cdn/shop/files/2.png?v=1745642562&width=1127']
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/pages/return-and-refund-policy', 'https://ecoop.in/collections/regular-water-bottles', 'https://ecoop.in/collections/premium-coffee-tumbler', 'https://ecoop.in/collections/protection-case-covers']
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:23:08,645 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:23:08,646 - services.reachability_service - INFO - [-] - [-] - Received reachability response: {"reachable_urls":}
2025-08-02 17:23:08,646 - services.reachability_service - INFO - [-] - [-] - === REACHABILITY RESPONSE ===
2025-08-02 17:23:08,646 - services.reachability_service - INFO - [-] - [-] - Raw response: {"reachable_urls":}
2025-08-02 17:23:08,646 - services.reachability_service - INFO - [-] - [-] - === END REACHABILITY RESPONSE ===
2025-08-02 17:23:08,646 - utils.json_parser - ERROR - [-] - [-] - Failed to decode JSON from API response for reachability check: Expecting value: line 1 column 19 (char 18)
2025-08-02 17:23:08,646 - utils.json_parser - ERROR - [-] - [-] - Raw response: {"reachable_urls":}
2025-08-02 17:23:08,646 - utils.json_parser - ERROR - [-] - [-] - Cleaned response: {"reachable_urls":}
2025-08-02 17:23:08,646 - services.reachability_service - WARNING - [-] - [-] - Failed to parse JSON response for batch: [20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
2025-08-02 17:23:08,646 - services.reachability_service - WARNING - [-] - [-] - Raw response that failed to parse: {"reachable_urls":}
2025-08-02 17:23:08,646 - services.reachability_service - INFO - [-] - [-] - Processing batch of 10 URLs (attempt 1/4)
2025-08-02 17:23:08,646 - services.reachability_service - INFO - [-] - [-] - Generated reachability prompt (length: 2289 characters)
2025-08-02 17:23:08,646 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:08,646 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 42 characters)
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "reachable_urls": [47]
}
```
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 771
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 527
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 1562
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/leaf-1550.png?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/palm-1848.png?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/offer-9677.png?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/5-stars-7239.png?v=1745646344&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/SOLAR_GLOW-1.jpg?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/walking-9075.png?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Forest_Sage-3.jpg?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/hand-coin-2966.png?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/handshake-3312.png?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 9
2025-08-02 17:23:18,863 - utils.gemini_client - WARNING - [-] - [-] - Failed URLs: ['https://ecoop.in/cdn/shop/files/leaf-1550.png?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/palm-1848.png?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/offer-9677.png?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/5-stars-7239.png?v=1745646344&width=600', 'https://ecoop.in/cdn/shop/files/SOLAR_GLOW-1.jpg?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/walking-9075.png?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/Forest_Sage-3.jpg?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/hand-coin-2966.png?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/handshake-3312.png?v=**********&width=600']
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living']
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:23:18,863 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Received reachability response: ```json
{
    "reachable_urls": [47]
}
```
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - === REACHABILITY RESPONSE ===
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Raw response: ```json
{
    "reachable_urls": [47]
}
```
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - === END REACHABILITY RESPONSE ===
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Found 1 reachable URLs in this batch: [47]
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Parsed JSON response: {'reachable_urls': [47]}
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Processing batch of 10 URLs (attempt 1/4)
2025-08-02 17:23:18,864 - services.reachability_service - INFO - [-] - [-] - Generated reachability prompt (length: 2516 characters)
2025-08-02 17:23:18,864 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:18,864 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 42 characters)
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "reachable_urls": [60]
}
```
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 937
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 681
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 1973
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.01.33_PM.png?v=1744457630&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.01.43_PM.png?v=1744457630&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.01.43_PM.png?v=1744457630&width=638',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.02.40_PM.png?v=1744457630&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.02.40_PM.png?v=1744457630&width=638',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.03.02_PM.png?v=1744457630&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-04-25_at_19.45.14.jpg?v=1746437567&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-11-12_at_21.44.32.jpg?v=1746449661&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-11-12_at_21.44.32.jpg?v=1746449661&width=768',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 9
2025-08-02 17:23:30,923 - utils.gemini_client - WARNING - [-] - [-] - Failed URLs: ['https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.01.33_PM.png?v=1744457630&width=600', 'https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.01.43_PM.png?v=1744457630&width=600', 'https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.01.43_PM.png?v=1744457630&width=638', 'https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.02.40_PM.png?v=1744457630&width=600', 'https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.02.40_PM.png?v=1744457630&width=638', 'https://ecoop.in/cdn/shop/files/Screenshot_2025-04-12_at_5.03.02_PM.png?v=1744457630&width=600', 'https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-04-25_at_19.45.14.jpg?v=1746437567&width=600', 'https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-11-12_at_21.44.32.jpg?v=1746449661&width=600', 'https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-11-12_at_21.44.32.jpg?v=1746449661&width=768']
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle']
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:23:30,923 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Received reachability response: ```json
{
    "reachable_urls": [60]
}
```
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - === REACHABILITY RESPONSE ===
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Raw response: ```json
{
    "reachable_urls": [60]
}
```
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - === END REACHABILITY RESPONSE ===
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Found 1 reachable URLs in this batch: [60]
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Parsed JSON response: {'reachable_urls': [60]}
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Processing batch of 8 URLs (attempt 1/4)
2025-08-02 17:23:30,924 - services.reachability_service - INFO - [-] - [-] - Generated reachability prompt (length: 2568 characters)
2025-08-02 17:23:30,924 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:30,924 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 42 characters)
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "reachable_urls": [84]
}
```
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 903
2025-08-02 17:23:41,861 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 591
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 1754
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle/?section_id=quick-view-content',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/preview_images/c74d9d2cb7a344a8a63c24aa873deba6.thumbnail.**********.jpg?v=**********&width=1920',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-07-21_at_11.40.36_fa35c401-55f6-42a0-adc1-01779aed06f2.jpg?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-08-14_at_13.47.26_50305e87-4533-4d22-81e9-5268a70646f9.jpg?v=**********&width=600',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/products/mocha-premium-canister-style-vacuum-insulated-stainless-steel-coffee-tumbler/?section_id=quick-view-content',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_ERROR: 'URL_RETRIEVAL_STATUS_ERROR'>
)]
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 6
2025-08-02 17:23:41,862 - utils.gemini_client - WARNING - [-] - [-] - Failed URLs: ['https://ecoop.in/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 'https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle/?section_id=quick-view-content', 'https://ecoop.in/cdn/shop/files/preview_images/c74d9d2cb7a344a8a63c24aa873deba6.thumbnail.**********.jpg?v=**********&width=1920', 'https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-07-21_at_11.40.36_fa35c401-55f6-42a0-adc1-01779aed06f2.jpg?v=**********&width=600', 'https://ecoop.in/cdn/shop/files/WhatsApp_Image_2024-08-14_at_13.47.26_50305e87-4533-4d22-81e9-5268a70646f9.jpg?v=**********&width=600', 'https://ecoop.in/products/mocha-premium-canister-style-vacuum-insulated-stainless-steel-coffee-tumbler/?section_id=quick-view-content']
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health']
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - Received reachability response: ```json
{
    "reachable_urls": [84]
}
```
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - === REACHABILITY RESPONSE ===
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - Raw response: ```json
{
    "reachable_urls": [84]
}
```
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - === END REACHABILITY RESPONSE ===
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - Found 1 reachable URLs in this batch: [84]
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - Parsed JSON response: {'reachable_urls': [84]}
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 17:23:41,862 - services.reachability_service - INFO - [-] - [-] - Found a total of 3 reachable URLs.
2025-08-02 17:23:41,862 - api.time_func - INFO - [-] - [-] - Function check_url_reachability finished in 54.03 seconds
2025-08-02 17:23:41,862 - api.main - INFO - [-] - [-] - Found 3 reachable URLs out of 88 processed
2025-08-02 17:23:41,862 - api.main - INFO - [-] - [-] - Step 3: Analyzing website health...
2025-08-02 17:23:41,862 - api.time_func - INFO - [-] - [-] - Function analyze_website_health starting
2025-08-02 17:23:41,862 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in
2025-08-02 17:23:41,862 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in (length: 5008 characters)
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in, URL context: True, Grounding: False
2025-08-02 17:23:41,862 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 17:23:41,863 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:41,863 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 17:23:41,863 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in
2025-08-02 17:23:41,863 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health (length: 5125 characters)
2025-08-02 17:23:41,863 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:41,863 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health, URL context: True, Grounding: False
2025-08-02 17:23:41,864 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 17:23:41,864 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:41,864 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 17:23:41,864 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 17:23:41,864 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living (length: 5065 characters)
2025-08-02 17:23:41,864 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:41,865 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living, URL context: True, Grounding: False
2025-08-02 17:23:41,865 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 17:23:41,865 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:41,865 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 17:23:41,865 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 17:23:41,866 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:41,866 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle (length: 5076 characters)
2025-08-02 17:23:41,866 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle, URL context: True, Grounding: False
2025-08-02 17:23:41,866 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 17:23:41,866 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:23:41,866 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 17:23:41,867 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 518 characters)
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Response text: {"navigation_issues_exists": "yes", 
"navigation_issue_type": ["Disabled Links", "Redirection to same page"],
"navigation_issues_area": ["Privacy policy (footer)", "Terms and Conditions (footer)", "Shipping Policy (footer)", "Refund Policy (footer)", "Cookies Policy (footer)"],
"redirection_same_page": "yes",
"links" : ["#"],
"phishing_site" : "no",
"phishing_reason": "N/A",
"malware_present": "no",
"malware_reason": "N/A",
"security_review_present" : "no",
"security_review": "N/A",
"security_review_source": []
}
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1059
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 223
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 1633
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health']
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:23:51,612 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health, result length: 518
2025-08-02 17:23:51,612 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health (length: 518 characters)
2025-08-02 17:23:51,612 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health ===
2025-08-02 17:23:51,612 - services.health_analysis_service - INFO - [-] - [-] - Raw response: {"navigation_issues_exists": "yes", 
"navigation_issue_type": ["Disabled Links", "Redirection to same page"],
"navigation_issues_area": ["Privacy policy (footer)", "Terms and Conditions (footer)", "Shipping Policy (footer)", "Refund Policy (footer)", "Cookies Policy (footer)"],
"redirection_same_page": "yes",
"links" : ["#"],
"phishing_site" : "no",
"phishing_reason": "N/A",
"malware_present": "no",
"malware_reason": "N/A",
"security_review_present" : "no",
"security_review": "N/A",
"security_review_source": []
}
2025-08-02 17:23:51,613 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 17:23:51,613 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health: {"navigation_issues_exists": "yes", 
"navigation_issue_type": ["Disabled Links", "Redirection to same page"],
"navigation_issues_area": ["Privacy policy (footer)", "Terms and Conditions (footer)", "Shipping Policy (footer)", "Refund Policy (footer)", "Cookies Policy (footer)"],
"redirection_same_page": "yes",
"links" : ["#"],
"phishing_site" : "no",
"phishing_reason": "N/A",
"malware_present": "no",
"malware_reason": "N/A",
"security_review_present" : "no",
"security_review": "N/A",
"security_review_source": []
}
2025-08-02 17:23:51,613 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 17:23:51,613 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 667 characters)
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "navigation_issues_exists": "no",
    "navigation_issue_type": [],
    "navigation_issues_area": [],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "Cannot assess phishing indicators or site reputation as a search/grounding tool is unavailable.",
    "malware_present": "no",
    "malware_reason": "Cannot investigate malware presence or security blacklists as a search/grounding tool is unavailable.",
    "security_review_present": "no",
    "security_review": "Cannot search for security reviews or scam reports as a search/grounding tool is unavailable.",
    "security_review_source": []
}
```
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1018
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 195
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 4167
2025-08-02 17:24:04,098 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:24:04,099 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 17:24:04,099 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 17:24:04,099 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in']
2025-08-02 17:24:04,099 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:24:04,099 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:24:04,099 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in, result length: 667
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in (length: 667 characters)
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in ===
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
    "navigation_issues_exists": "no",
    "navigation_issue_type": [],
    "navigation_issues_area": [],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "Cannot assess phishing indicators or site reputation as a search/grounding tool is unavailable.",
    "malware_present": "no",
    "malware_reason": "Cannot investigate malware presence or security blacklists as a search/grounding tool is unavailable.",
    "security_review_present": "no",
    "security_review": "Cannot search for security reviews or scam reports as a search/grounding tool is unavailable.",
    "security_review_source": []
}
```
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in: {
    "navigation_issues_exists": "no",
    "navigation_issue_type": [],
    "navigation_issues_area": [],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "Cannot assess phishing indicators or site reputation as a search/grounding tool is unavailable.",
    "malware_present": "no",
    "malware_reason": "Cannot investigate malware presence or security blacklists as a search/grounding tool is unavailable.",
    "security_review_present": "no",
    "security_review": "Cannot search for security reviews or scam reports as a search/grounding tool is unavailable.",
    "security_review_source": []
}
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in
2025-08-02 17:24:04,099 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 557 characters)
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
  "navigation_issues_exists": "no",
  "navigation_issue_type": [],
  "navigation_issues_area": [],
  "redirection_same_page": "no",
  "links": [],
  "phishing_site": "no",
  "phishing_reason": "No obvious phishing indicators or suspicious elements on the page content.",
  "malware_present": "no",
  "malware_reason": "No direct malware indicators found on the browsed page content.",
  "security_review_present": "no",
  "security_review": "Unable to perform external search for security reviews or reports.",
  "security_review_source": []
}
```
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1044
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 293
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 5708
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:24:09,667 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 17:24:09,668 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 17:24:09,668 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle']
2025-08-02 17:24:09,668 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:24:09,668 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:24:09,668 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle, result length: 557
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle (length: 557 characters)
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle ===
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
  "navigation_issues_exists": "no",
  "navigation_issue_type": [],
  "navigation_issues_area": [],
  "redirection_same_page": "no",
  "links": [],
  "phishing_site": "no",
  "phishing_reason": "No obvious phishing indicators or suspicious elements on the page content.",
  "malware_present": "no",
  "malware_reason": "No direct malware indicators found on the browsed page content.",
  "security_review_present": "no",
  "security_review": "Unable to perform external search for security reviews or reports.",
  "security_review_source": []
}
```
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle: {
  "navigation_issues_exists": "no",
  "navigation_issue_type": [],
  "navigation_issues_area": [],
  "redirection_same_page": "no",
  "links": [],
  "phishing_site": "no",
  "phishing_reason": "No obvious phishing indicators or suspicious elements on the page content.",
  "malware_present": "no",
  "malware_reason": "No direct malware indicators found on the browsed page content.",
  "security_review_present": "no",
  "security_review": "Unable to perform external search for security reviews or reports.",
  "security_review_source": []
}
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 17:24:09,668 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 17:24:22,597 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 635 characters)
2025-08-02 17:24:22,597 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:24:22,597 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "navigation_issues_exists": "no",
    "navigation_issue_type": [],
    "navigation_issues_area": [],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "No suspicious content, unusual URL, or requests for sensitive data were identified. [1]",
    "malware_present": "no",
    "malware_reason": "External security scans and blacklisting checks are required to confirm malware presence.",
    "security_review_present": "no",
    "security_review": "External security reviews and user reports are needed to confirm security issues.",
    "security_review_source": []
}
```
2025-08-02 17:24:22,597 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:24:22,598 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:24:22,598 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:24:22,598 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1038
2025-08-02 17:24:22,598 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 213
2025-08-02 17:24:22,598 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 7245
2025-08-02 17:24:22,598 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 17:24:22,599 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 17:24:22,599 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 17:24:22,599 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living']
2025-08-02 17:24:22,599 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 17:24:22,599 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:24:22,600 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living, result length: 635
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living (length: 635 characters)
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living ===
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
    "navigation_issues_exists": "no",
    "navigation_issue_type": [],
    "navigation_issues_area": [],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "No suspicious content, unusual URL, or requests for sensitive data were identified. [1]",
    "malware_present": "no",
    "malware_reason": "External security scans and blacklisting checks are required to confirm malware presence.",
    "security_review_present": "no",
    "security_review": "External security reviews and user reports are needed to confirm security issues.",
    "security_review_source": []
}
```
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living: {
    "navigation_issues_exists": "no",
    "navigation_issue_type": [],
    "navigation_issues_area": [],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "No suspicious content, unusual URL, or requests for sensitive data were identified. [1]",
    "malware_present": "no",
    "malware_reason": "External security scans and blacklisting checks are required to confirm malware presence.",
    "security_review_present": "no",
    "security_review": "External security reviews and user reports are needed to confirm security issues.",
    "security_review_source": []
}
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 17:24:22,600 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 17:24:22,600 - api.time_func - INFO - [-] - [-] - Function analyze_website_health finished in 40.74 seconds
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - Website health analysis complete for 4 URLs
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - === ANALYSIS RESULTS SUMMARY ===
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - URL 1 analysis completed successfully for: https://ecoop.in
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Navigation issues: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - URL 2 analysis completed successfully for: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Navigation issues: yes
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - URL 3 analysis completed successfully for: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Navigation issues: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - URL 4 analysis completed successfully for: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Navigation issues: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 17:24:22,600 - api.main - INFO - [-] - [-] - === END ANALYSIS RESULTS SUMMARY ===
2025-08-02 17:24:22,600 - api.time_func - INFO - [-] - [-] - Function save_complete_analysis starting
2025-08-02 17:24:22,620 - services.database_service - INFO - [-] - [-] - Request with ref_id 3aebbda5-da77-410c-b612-7b3ede3a7fd8 already exists, updating...
2025-08-02 17:24:22,620 - services.database_service - INFO - [-] - [-] - Successfully updated request with ref_id 3aebbda5-da77-410c-b612-7b3ede3a7fd8
2025-08-02 17:24:22,627 - services.database_service - INFO - [-] - [-] - Successfully committed analysis result with ID: 10
2025-08-02 17:24:22,628 - services.database_service - INFO - [-] - [-] - Saved analysis result with ID: 10
2025-08-02 17:24:22,632 - services.database_service - INFO - [-] - [-] - Successfully committed 4 URL analyses.
2025-08-02 17:24:22,634 - services.database_service - INFO - [-] - [-] - Saved 4 URL analyses
2025-08-02 17:24:22,636 - api.time_func - INFO - [-] - [-] - Function save_complete_analysis finished in 0.04 seconds
2025-08-02 17:24:22,636 - api.main - INFO - [-] - [-] - Analysis data saved to database: {'request_id': 2, 'result_id': 10, 'summary': {'total_urls': 12, 'navigation_issues_count': 2, 'phishing_sites_count': 0, 'malware_sites_count': 0, 'security_reviews_count': 0, 'navigation_issues_percentage': 16.666666666666664, 'phishing_sites_percentage': 0.0, 'malware_sites_percentage': 0.0}, 'status': 'success'}
2025-08-02 17:24:22,636 - api.main - INFO - [-] - [-] - End time: **********.6367342
2025-08-02 17:24:22,636 - api.time_func - INFO - [-] - [-] - Function analyze_health finished in 94.80 seconds
INFO:     127.0.0.1:50202 - "POST /analyze-website-health HTTP/1.1" 200 OK
2025-08-02 17:54:49,144 - api.time_func - INFO - [-] - [-] - Function analyze_presence starting
2025-08-02 17:54:49,145 - api.main - INFO - [-] - [-] - Starting presence analysis for website: https://ecoop.in/
2025-08-02 17:54:49,145 - api.time_func - INFO - [-] - [-] - Function analyze_website_presence starting
2025-08-02 17:54:49,145 - services.presence_analysis_service - INFO - [-] - [-] - Starting presence analysis for URL: https://ecoop.in/
2025-08-02 17:54:49,145 - services.presence_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/ (length: 2864 characters)
2025-08-02 17:54:49,145 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/, URL context: False, Grounding: True
2025-08-02 17:54:49,145 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 17:54:49,145 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 17:54:49,145 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/
2025-08-02 17:54:49,145 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: False, Grounding: True, Tools count: 1
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 127 characters)
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
"website_functional": "yes",
"search_rank": "low",
"reviews": [],
"traffic": "low",
"social_media_presence": []
}
```
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 634
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 164
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 2921
2025-08-02 17:55:08,035 - utils.gemini_client - INFO - [-] - [-] - No URL context metadata available in response
2025-08-02 17:55:08,036 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 17:55:08,036 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/, result length: 127
2025-08-02 17:55:08,036 - services.presence_analysis_service - INFO - [-] - [-] - Received presence analysis response for https://ecoop.in/ (length: 127 characters)
2025-08-02 17:55:08,036 - services.presence_analysis_service - INFO - [-] - [-] - === PRESENCE ANALYSIS RESPONSE FOR https://ecoop.in/ ===
2025-08-02 17:55:08,036 - services.presence_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
"website_functional": "yes",
"search_rank": "low",
"reviews": [],
"traffic": "low",
"social_media_presence": []
}
```
2025-08-02 17:55:08,036 - services.presence_analysis_service - INFO - [-] - [-] - === END PRESENCE ANALYSIS RESPONSE ===
2025-08-02 17:55:08,036 - services.presence_analysis_service - INFO - [-] - [-] - Successfully parsed presence analysis for https://ecoop.in/
2025-08-02 17:55:08,036 - services.presence_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['website_functional', 'search_rank', 'reviews', 'traffic', 'social_media_presence', 'analyzed_url']
2025-08-02 17:55:08,036 - api.time_func - INFO - [-] - [-] - Function analyze_website_presence finished in 18.89 seconds
2025-08-02 17:55:08,036 - api.main - INFO - [-] - [-] - Presence analysis completed successfully for https://ecoop.in/
2025-08-02 17:55:08,036 - api.time_func - INFO - [-] - [-] - Function analyze_presence finished in 18.89 seconds
INFO:     127.0.0.1:34276 - "POST /analyze-website-presence HTTP/1.1" 200 OK
WARNING:  StatReload detected changes in 'api/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9003]
2025-08-02 18:32:58,805 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
2025-08-02 18:32:58,825 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
INFO:     Started server process [18571]
INFO:     Waiting for application startup.
2025-08-02 18:32:58,853 - database.config - INFO - [-] - [-] - Database tables created successfully
2025-08-02 18:32:58,853 - api.main - INFO - [-] - [-] - Database initialized successfully
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'api/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [18571]
2025-08-02 18:33:11,347 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
2025-08-02 18:33:11,367 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
INFO:     Started server process [18614]
INFO:     Waiting for application startup.
2025-08-02 18:33:11,393 - database.config - INFO - [-] - [-] - Database tables created successfully
2025-08-02 18:33:11,393 - api.main - INFO - [-] - [-] - Database initialized successfully
INFO:     Application startup complete.
WARNING:  StatReload detected changes in 'api/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [18614]
2025-08-02 18:33:20,742 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
2025-08-02 18:33:20,761 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
INFO:     Started server process [18660]
INFO:     Waiting for application startup.
2025-08-02 18:33:20,787 - database.config - INFO - [-] - [-] - Database tables created successfully
2025-08-02 18:33:20,787 - api.main - INFO - [-] - [-] - Database initialized successfully
INFO:     Application startup complete.
2025-08-02 18:33:57,901 - api.main - WARNING - [-] - [-] - Database health check failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
INFO:     127.0.0.1:37964 - "GET /health-check HTTP/1.1" 200 OK
2025-08-02 18:34:03,465 - api.main - WARNING - [-] - [-] - Database health check failed: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
INFO:     127.0.0.1:37970 - "GET /health-check HTTP/1.1" 200 OK
2025-08-02 18:34:03,471 - api.time_func - INFO - [-] - [-] - Function analyze_health starting
2025-08-02 18:34:03,471 - api.main - INFO - [-] - [-] - Received request for website: https://ecoop.in
2025-08-02 18:34:03,471 - api.main - INFO - [-] - [-] - Start time: **********.4713502
2025-08-02 18:34:03,471 - api.main - INFO - [-] - [-] - Step 1: Processing URLs...
2025-08-02 18:34:03,471 - utils.url_processor - INFO - [-] - [-] - Processing URLs from 2 depth items
2025-08-02 18:34:03,471 - utils.url_processor - INFO - [-] - [-] - Total URLs extracted: 4
2025-08-02 18:34:03,472 - utils.url_processor - INFO - [-] - [-] - Valid URLs after filtering: 4
2025-08-02 18:34:03,472 - utils.url_processor - INFO - [-] - [-] - Unique URLs after deduplication: 4
2025-08-02 18:34:03,472 - utils.url_processor - INFO - [-] - [-] - URLs after sorting by length and taking top 100: 4
2025-08-02 18:34:03,472 - api.main - INFO - [-] - [-] - Processed 4 unique URLs from 4 total URLs
2025-08-02 18:34:03,472 - api.main - INFO - [-] - [-] - Step 2: Checking URL reachability...
2025-08-02 18:34:03,472 - api.time_func - INFO - [-] - [-] - Function check_url_reachability starting
2025-08-02 18:34:03,472 - services.reachability_service - INFO - [-] - [-] - Starting reachability check for 4 URLs using Gemini URL context tool
2025-08-02 18:34:03,472 - services.reachability_service - INFO - [-] - [-] - Processing batch of 4 URLs (attempt 1/4)
2025-08-02 18:34:03,472 - services.reachability_service - INFO - [-] - [-] - Generated reachability prompt (length: 1851 characters)
2025-08-02 18:34:03,491 - utils.gemini_client - INFO - [-] - [-] - Gemini client initialized successfully
2025-08-02 18:34:03,492 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 18:34:03,492 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 18:34:12,187 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 50 characters)
2025-08-02 18:34:12,187 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 18:34:12,187 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "reachable_urls": [0, 1, 2, 3]
}
```
2025-08-02 18:34:12,187 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 462
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 162
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 794
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
), UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 4, Failed: 0
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in', 'https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living', 'https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle', 'https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health']
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Received reachability response: ```json
{
    "reachable_urls": [0, 1, 2, 3]
}
```
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - === REACHABILITY RESPONSE ===
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Raw response: ```json
{
    "reachable_urls": [0, 1, 2, 3]
}
```
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - === END REACHABILITY RESPONSE ===
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Found 4 reachable URLs in this batch: [0, 1, 2, 3]
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Parsed JSON response: {'reachable_urls': [0, 1, 2, 3]}
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Added reachable URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 18:34:12,188 - services.reachability_service - INFO - [-] - [-] - Found a total of 4 reachable URLs.
2025-08-02 18:34:12,188 - api.time_func - INFO - [-] - [-] - Function check_url_reachability finished in 8.72 seconds
2025-08-02 18:34:12,188 - api.main - INFO - [-] - [-] - Found 4 reachable URLs out of 4 processed
2025-08-02 18:34:12,188 - api.main - INFO - [-] - [-] - Step 3: Analyzing website health...
2025-08-02 18:34:12,188 - api.time_func - INFO - [-] - [-] - Function analyze_website_health starting
2025-08-02 18:34:12,188 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in
2025-08-02 18:34:12,188 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in (length: 5008 characters)
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in, URL context: True, Grounding: False
2025-08-02 18:34:12,188 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 18:34:12,189 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 18:34:12,189 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 18:34:12,189 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in
2025-08-02 18:34:12,189 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health (length: 5125 characters)
2025-08-02 18:34:12,189 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 18:34:12,190 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health, URL context: True, Grounding: False
2025-08-02 18:34:12,191 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 18:34:12,191 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 18:34:12,191 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 18:34:12,191 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 18:34:12,191 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living (length: 5065 characters)
2025-08-02 18:34:12,191 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 18:34:12,191 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living, URL context: True, Grounding: False
2025-08-02 18:34:12,192 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 18:34:12,192 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 18:34:12,192 - services.health_analysis_service - INFO - [-] - [-] - Starting health analysis for URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 18:34:12,192 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 18:34:12,192 - services.health_analysis_service - INFO - [-] - [-] - Generated prompt for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle (length: 5076 characters)
2025-08-02 18:34:12,192 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 18:34:12,192 - utils.gemini_client - INFO - [-] - [-] - Async request initiated for URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle, URL context: True, Grounding: False
2025-08-02 18:34:12,193 - utils.gemini_client - INFO - [-] - [-] - Note: New Google AI SDK doesn't support async methods, falling back to sync via thread pool
2025-08-02 18:34:12,193 - utils.gemini_client - INFO - [-] - [-] - Making request to Gemini API (attempt 1/4)...
2025-08-02 18:34:12,193 - utils.gemini_client - INFO - [-] - [-] - Including URL context: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 18:34:12,193 - utils.gemini_client - INFO - [-] - [-] - Request details - Model: gemini-2.5-flash, URL context: True, Grounding: False, Tools count: 1
2025-08-02 18:34:33,497 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 631 characters)
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
"navigation_issues_exists": "yes",
"navigation_issue_type": ["Redirection to same page"],
"navigation_issues_area": ["Skip to content link"],
"redirection_same_page": "yes",
"links": ["#"],
"phishing_site": "no",
"phishing_reason": "The site appears legitimate with standard e-commerce features and a secure URL. [1]",
"malware_present": "no",
"malware_reason": "No indicators of malware or suspicious scripts were found in the content. [1]",
"security_review_present": "no",
"security_review": "No security-related user reviews or blacklistings were found within the browsed content.",
"security_review_source": []
}
```
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1018
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 194
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 3780
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in']
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 18:34:33,498 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in, result length: 631
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in (length: 631 characters)
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in ===
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
"navigation_issues_exists": "yes",
"navigation_issue_type": ["Redirection to same page"],
"navigation_issues_area": ["Skip to content link"],
"redirection_same_page": "yes",
"links": ["#"],
"phishing_site": "no",
"phishing_reason": "The site appears legitimate with standard e-commerce features and a secure URL. [1]",
"malware_present": "no",
"malware_reason": "No indicators of malware or suspicious scripts were found in the content. [1]",
"security_review_present": "no",
"security_review": "No security-related user reviews or blacklistings were found within the browsed content.",
"security_review_source": []
}
```
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in: {
"navigation_issues_exists": "yes",
"navigation_issue_type": ["Redirection to same page"],
"navigation_issues_area": ["Skip to content link"],
"redirection_same_page": "yes",
"links": ["#"],
"phishing_site": "no",
"phishing_reason": "The site appears legitimate with standard e-commerce features and a secure URL. [1]",
"malware_present": "no",
"malware_reason": "No indicators of malware or suspicious scripts were found in the content. [1]",
"security_review_present": "no",
"security_review": "No security-related user reviews or blacklistings were found within the browsed content.",
"security_review_source": []
}
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in
2025-08-02 18:34:33,498 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 18:34:36,584 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 828 characters)
2025-08-02 18:34:36,584 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
  "navigation_issues_exists": "yes",
  "navigation_issue_type": ["JavaScript Errors"],
  "navigation_issues_area": ["Email submission form 'Submit' button (due to 'Email is not in correct format.' error preventing submission)"],
  "redirection_same_page": "no",
  "links": [],
  "phishing_site": "cannot be determined",
  "phishing_reason": "Unable to perform external searches for reputation or suspicious elements due to tool limitations.",
  "malware_present": "cannot be determined",
  "malware_reason": "Unable to perform external searches for security blacklists or reports due to tool limitations.",
  "security_review_present": "cannot be determined",
  "security_review": "Unable to perform external searches for user reviews or security reports due to tool limitations.",
  "security_review_source": []
}
```
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1059
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 360
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 4981
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health']
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 18:34:36,585 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health, result length: 828
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health (length: 828 characters)
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health ===
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
  "navigation_issues_exists": "yes",
  "navigation_issue_type": ["JavaScript Errors"],
  "navigation_issues_area": ["Email submission form 'Submit' button (due to 'Email is not in correct format.' error preventing submission)"],
  "redirection_same_page": "no",
  "links": [],
  "phishing_site": "cannot be determined",
  "phishing_reason": "Unable to perform external searches for reputation or suspicious elements due to tool limitations.",
  "malware_present": "cannot be determined",
  "malware_reason": "Unable to perform external searches for security blacklists or reports due to tool limitations.",
  "security_review_present": "cannot be determined",
  "security_review": "Unable to perform external searches for user reviews or security reports due to tool limitations.",
  "security_review_source": []
}
```
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health: {
  "navigation_issues_exists": "yes",
  "navigation_issue_type": ["JavaScript Errors"],
  "navigation_issues_area": ["Email submission form 'Submit' button (due to 'Email is not in correct format.' error preventing submission)"],
  "redirection_same_page": "no",
  "links": [],
  "phishing_site": "cannot be determined",
  "phishing_reason": "Unable to perform external searches for reputation or suspicious elements due to tool limitations.",
  "malware_present": "cannot be determined",
  "malware_reason": "Unable to perform external searches for security blacklists or reports due to tool limitations.",
  "security_review_present": "cannot be determined",
  "security_review": "Unable to perform external searches for user reviews or security reports due to tool limitations.",
  "security_review_source": []
}
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 18:34:36,585 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 18:34:39,286 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 787 characters)
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
    "navigation_issues_exists": "yes",
    "navigation_issue_type": ["JavaScript Errors", "Non-standard Navigation"],
    "navigation_issues_area": ["Image/content display (NaN / 9 indication) [1]"],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "The URL uses HTTPS, and the content appears to be a legitimate product page without suspicious language or elements. [1]",
    "malware_present": "no",
    "malware_reason": "Could not verify through external security reports or blacklists as no search tool is available.",
    "security_review_present": "no",
    "security_review": "Could not verify through external security reports or user reviews as no search tool is available.",
    "security_review_source": []
}
```
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1044
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 250
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 4964
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle']
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 18:34:39,287 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle, result length: 787
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle (length: 787 characters)
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle ===
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
    "navigation_issues_exists": "yes",
    "navigation_issue_type": ["JavaScript Errors", "Non-standard Navigation"],
    "navigation_issues_area": ["Image/content display (NaN / 9 indication) [1]"],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "The URL uses HTTPS, and the content appears to be a legitimate product page without suspicious language or elements. [1]",
    "malware_present": "no",
    "malware_reason": "Could not verify through external security reports or blacklists as no search tool is available.",
    "security_review_present": "no",
    "security_review": "Could not verify through external security reports or user reviews as no search tool is available.",
    "security_review_source": []
}
```
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle: {
    "navigation_issues_exists": "yes",
    "navigation_issue_type": ["JavaScript Errors", "Non-standard Navigation"],
    "navigation_issues_area": ["Image/content display (NaN / 9 indication) [1]"],
    "redirection_same_page": "no",
    "links": [],
    "phishing_site": "no",
    "phishing_reason": "The URL uses HTTPS, and the content appears to be a legitimate product page without suspicious language or elements. [1]",
    "malware_present": "no",
    "malware_reason": "Could not verify through external security reports or blacklists as no search tool is available.",
    "security_review_present": "no",
    "security_review": "Could not verify through external security reports or user reviews as no search tool is available.",
    "security_review_source": []
}
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 18:34:39,287 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Received response from Gemini API (length: 631 characters)
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - === GEMINI RESPONSE CONTENT ===
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Response text: ```json
{
"navigation_issues_exists": "no",
"navigation_issue_type": [],
"navigation_issues_area": [],
"redirection_same_page": "no",
"links": [],
"phishing_site": "no",
"phishing_reason": "No clear phishing indicators observed; cannot fully verify site reputation due to tool limitations.",
"malware_present": "no",
"malware_reason": "No malware found in content; external blacklists/reports could not be checked due to tool limitations.",
"security_review_present": "no",
"security_review": "No security reviews or scam reports found; external search was not possible due to tool limitations.",
"security_review_source": []
}
```
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - === END GEMINI RESPONSE ===
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Response metadata - Candidates count: 1
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - First candidate finish reason: FinishReason.STOP
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Prompt token count: 1038
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Candidates token count: 307
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Usage metadata - Total token count: 5895
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - === URL CONTEXT METADATA ===
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - URL context metadata: url_metadata=[UrlMetadata(
  retrieved_url='https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living',
  url_retrieval_status=<UrlRetrievalStatus.URL_RETRIEVAL_STATUS_SUCCESS: 'URL_RETRIEVAL_STATUS_SUCCESS'>
)]
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - URL retrieval summary - Successful: 1, Failed: 0
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Successful URLs: ['https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living']
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - === END URL CONTEXT METADATA ===
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - No safety ratings in response
2025-08-02 18:34:44,407 - utils.gemini_client - INFO - [-] - [-] - Async request completed for URL: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living, result length: 631
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - Received health analysis response for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living (length: 631 characters)
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - === HEALTH ANALYSIS RESPONSE FOR https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living ===
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - Raw response: ```json
{
"navigation_issues_exists": "no",
"navigation_issue_type": [],
"navigation_issues_area": [],
"redirection_same_page": "no",
"links": [],
"phishing_site": "no",
"phishing_reason": "No clear phishing indicators observed; cannot fully verify site reputation due to tool limitations.",
"malware_present": "no",
"malware_reason": "No malware found in content; external blacklists/reports could not be checked due to tool limitations.",
"security_review_present": "no",
"security_review": "No security reviews or scam reports found; external search was not possible due to tool limitations.",
"security_review_source": []
}
```
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - === END HEALTH ANALYSIS RESPONSE ===
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - Cleaned response for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living: {
"navigation_issues_exists": "no",
"navigation_issue_type": [],
"navigation_issues_area": [],
"redirection_same_page": "no",
"links": [],
"phishing_site": "no",
"phishing_reason": "No clear phishing indicators observed; cannot fully verify site reputation due to tool limitations.",
"malware_present": "no",
"malware_reason": "No malware found in content; external blacklists/reports could not be checked due to tool limitations.",
"security_review_present": "no",
"security_review": "No security reviews or scam reports found; external search was not possible due to tool limitations.",
"security_review_source": []
}
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - Successfully parsed health analysis for https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 18:34:44,408 - services.health_analysis_service - INFO - [-] - [-] - Parsed JSON keys: ['navigation_issues_exists', 'navigation_issue_type', 'navigation_issues_area', 'redirection_same_page', 'links', 'phishing_site', 'phishing_reason', 'malware_present', 'malware_reason', 'security_review_present', 'security_review', 'security_review_source', 'analyzed_url']
2025-08-02 18:34:44,408 - api.time_func - INFO - [-] - [-] - Function analyze_website_health finished in 32.22 seconds
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - Website health analysis complete for 4 URLs
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - === ANALYSIS RESULTS SUMMARY ===
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - URL 1 analysis completed successfully for: https://ecoop.in
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Navigation issues: yes
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - URL 2 analysis completed successfully for: https://ecoop.in/blogs/news/how-ro-purification-is-harmful-removing-essential-minerals-from-water-and-its-long-term-effects-on-health
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Navigation issues: yes
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Phishing site: cannot be determined
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Malware present: cannot be determined
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - URL 3 analysis completed successfully for: https://ecoop.in/blogs/news/the-vital-role-of-hydration-in-healthy-living
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Navigation issues: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - URL 4 analysis completed successfully for: https://ecoop.in/products/go-mini-hpf-3s-3-stage-nano-technology-water-filter-bottle
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Navigation issues: yes
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Phishing site: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] -   - Malware present: no
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - === END ANALYSIS RESULTS SUMMARY ===
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - Generating aggregated website health payload...
2025-08-02 18:34:44,408 - services.payload_aggregator - INFO - [-] - [-] - Starting website health payload aggregation
2025-08-02 18:34:44,408 - services.payload_aggregator - INFO - [-] - [-] - Aggregating 4 URL analysis results
2025-08-02 18:34:44,408 - services.payload_aggregator - INFO - [-] - [-] - Successfully aggregated website health payload
2025-08-02 18:34:44,408 - api.main - INFO - [-] - [-] - Successfully generated aggregated payload
2025-08-02 18:34:44,408 - api.time_func - INFO - [-] - [-] - Function save_complete_analysis starting
2025-08-02 18:34:44,434 - services.database_service - INFO - [-] - [-] - Successfully committed new analysis request with ID: 7
2025-08-02 18:34:44,435 - services.database_service - INFO - [-] - [-] - Saved analysis request with ID: 7
2025-08-02 18:34:44,440 - services.database_service - INFO - [-] - [-] - Successfully committed analysis result with ID: 11
2025-08-02 18:34:44,441 - services.database_service - INFO - [-] - [-] - Saved analysis result with ID: 11
2025-08-02 18:34:44,446 - services.database_service - INFO - [-] - [-] - Successfully committed 4 URL analyses.
2025-08-02 18:34:44,448 - services.database_service - INFO - [-] - [-] - Saved 4 URL analyses
2025-08-02 18:34:44,450 - api.time_func - INFO - [-] - [-] - Function save_complete_analysis finished in 0.04 seconds
2025-08-02 18:34:44,450 - api.main - INFO - [-] - [-] - Analysis data saved to database: {'request_id': 7, 'result_id': 11, 'summary': {'total_urls': 4, 'navigation_issues_count': 3, 'phishing_sites_count': 0, 'malware_sites_count': 0, 'security_reviews_count': 0, 'navigation_issues_percentage': 75.0, 'phishing_sites_percentage': 0.0, 'malware_sites_percentage': 0.0}, 'status': 'success'}
2025-08-02 18:34:44,450 - api.main - INFO - [-] - [-] - End time: **********.***********-08-02 18:34:44,450 - api.time_func - INFO - [-] - [-] - Function analyze_health finished in 40.98 seconds
INFO:     127.0.0.1:37974 - "POST /analyze-website-health HTTP/1.1" 200 OK

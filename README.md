# Website Health Analysis API

A production-ready API for analyzing website health, security, and usability using Google's Gemini 2.5 Flash AI model with URL context capabilities.

## Features

- **URL Processing**: Deduplicates, sorts, and processes up to 100 URLs
- **Reachability Testing**: Checks URL accessibility in batches of 15 (respects Gemini's 20 URL limit)
- **Security Analysis**: Comprehensive security and usability assessment
- **AI-Powered**: Uses Gemini 2.5 Flash with URL context tool and grounding
- **Result Aggregation**: Automatically consolidates findings from multiple URLs into a single summary
- **Database Storage**: SQLite3 database for storing requests, results, and analysis data
- **Production Ready**: Includes logging, error handling, and retry logic
- **RESTful API**: FastAPI-based with automatic documentation

## Architecture

### Flow Overview

1. **URL Extraction**: Get all extracted URLs from request
2. **Deduplication**: Remove duplicate URLs
3. **Sorting & Selection**: Sort by length (ascending), take top 100, convert to dictionary
4. **Reachability Check**: Send 15 URLs at once to Gemini, iterate until 4 reachable URLs found
5. **Final Deduplication**: Combine reachable URLs with home page URL (max 5 URLs)
6. **Health Analysis**: Analyze each URL individually for security and usability issues
7. **Database Storage**: Save request, results, and individual URL analyses to SQLite3

### Components

- **API Layer** (`api/`): FastAPI endpoints and request models
- **Services** (`services/`): Business logic for reachability, health analysis, and database operations
- **Database** (`database/`): SQLAlchemy models and configuration
- **Utils** (`utils/`): Shared utilities including Gemini client and prompts
- **Logging**: Comprehensive logging throughout the application

## URL Limit Handling

The application properly handles Gemini's URL context tool limitations:

- **Gemini URL Context Limit**: 20 URLs per request
- **Default Batch Size**: 15 URLs (conservative to avoid hitting the limit)
- **Retry Logic**: Automatically reduces batch size by half if limit is exceeded
- **Error Recovery**: Graceful handling of "Number of urls to lookup exceeds the limit" errors

This ensures reliable operation even with large numbers of URLs to process.

## Database Schema

The application uses three main tables:

### AnalysisRequest
- Stores incoming analysis requests
- Tracks request metadata (ref ID, website URL, org ID)
- Links to analysis results and URL analyses

### AnalysisResult
- Stores overall analysis results
- Contains processing statistics and timing
- Links to individual URL analyses

### UrlAnalysis
- Stores detailed analysis for each URL
- Includes navigation issues and security assessment
- Preserves raw analysis data for audit purposes

## Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd newtool
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables**
```bash
export GEMINI_API_KEY="your-gemini-api-key"
export DB_PATH="website_health_analysis.db"  # Optional, defaults to this value
```

4. **Set up SQLite3 database**
```bash
python setup_database.py
```

## Usage

### Starting the API

```bash
# Using the startup script
python run_api.py

# Or directly with uvicorn
uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
```

### API Endpoints

#### Health Check
```bash
GET /health-check
```

#### Website Health Analysis
```bash
POST /analyze-website-health
```

**Request Body:**
```json
{
  "website": "https://example.com",
  "scrapeRequestRefID": "REQ12345",
  "parsed_urls": [
    {
      "url_depth": 1,
      "urls": ["https://example.com/page1", "https://example.com/page2"]
    },
    {
      "url_depth": 2,
      "urls": ["https://example.com/page3", "https://example.com/page4"]
    }
  ],
  "org_id": "**********"
}
```

**Response:**
```json
{
  "status": "success",
  "processed_urls": 48,
  "reachable_urls": 4,
  "analyzed_urls": 4,
  "processing_time_seconds": 45.23,
  "results": [...],
  "database_info": {
    "request_id": 1,
    "result_id": 1,
    "summary": {
      "total_urls": 4,
      "navigation_issues_count": 2,
      "phishing_sites_count": 0,
      "malware_sites_count": 0
    },
    "status": "success"
  }
}
```

#### Analysis History
```bash
GET /analysis-history?org_id=**********&limit=10
```

#### Analysis Summary
```bash
GET /analysis-summary/{request_id}
```

#### Analysis Details
```bash
GET /analysis-details/{ref_id}
```

## Result Aggregation

The API automatically aggregates analysis results from multiple URLs into a consolidated summary. This provides both detailed per-URL results and an overall assessment.

### Aggregation Rules

- **Boolean Fields**: If ANY URL has "yes", the aggregated result is "yes"
  - `navigation_issues_exists`, `redirection_same_page`, `phishing_site`, `malware_present`, `security_review_present`
- **List Fields**: All values from all URLs are combined (duplicates removed)
  - `navigation_issue_type`, `navigation_issues_area`, `links`, `security_review_source`, `analyzed_url`
- **Reason Fields**: String values converted to lists and combined
  - `phishing_reason`, `malware_reason`, `security_review`

### Response Structure

```json
{
  "status": "success",
  "results": [...],
  "aggregated_analysis": {
    "navigation_issues_exists": "yes",
    "navigation_issue_type": ["Broken Links", "Disabled Forms"],
    "phishing_site": "no",
    "phishing_reason": ["No indicators found", "Site appears legitimate"],
    "analyzed_url": ["https://example.com/page1", "https://example.com/page2"]
  }
}
```

### Standalone Aggregation

Use the standalone utility to aggregate existing results:

```bash
# Aggregate from JSON file
python3 utils/aggregate_payload.py input_results.json output_summary.json

# View help
python3 utils/aggregate_payload.py --help
```

See `docs/AGGREGATION_GUIDE.md` for detailed documentation.

## Testing

### Run the test script
```bash
python test_flow.py
```

This will test:
- Gemini client connectivity
- Complete URL processing flow
- Reachability checking
- Health analysis

## Configuration

### Environment Variables

- `GEMINI_API_KEY`: Required. Your Google Gemini API key
- `DB_PATH`: Optional. SQLite3 database file path (default: website_health_analysis.db)
- `HOST`: Optional. Server host (default: 0.0.0.0)
- `PORT`: Optional. Server port (default: 8000)
- `RELOAD`: Optional. Enable auto-reload (default: true)

### Gemini 2.5 Flash Features

The application uses:
- **URL Context Tool**: For accessing and analyzing website content
- **Grounding**: For searching security reports and reviews
- **Retry Logic**: 3 attempts with exponential backoff
- **Response Validation**: Comprehensive error handling

## Security Analysis Features

### Navigation Issues
- Broken links (404 errors)
- JavaScript errors
- Login walls
- Infinite redirect loops
- Disabled/dummy links
- Non-standard navigation

### Security Assessment
- Phishing indicators
- Malware risk assessment
- Security threat listings
- User reviews and scam reports

## Production Considerations

- **Logging**: Comprehensive logging with configurable levels
- **Error Handling**: Graceful error handling with detailed error messages
- **Retry Logic**: Automatic retries for API failures
- **Database**: SQLite3 database for persistent storage (file-based, no server required)
- **Rate Limiting**: Consider implementing rate limiting for production use
- **Monitoring**: Add health checks and monitoring endpoints
- **Security**: Implement authentication and authorization as needed

## API Documentation

When the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## License

[Add your license information here]
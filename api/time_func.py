import functools
from utils.logger import get_logger
import time
import asyncio

logger = get_logger(__name__)

def timeit(func):
    if asyncio.iscoroutinefunction(func):
        # Handle async functions
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            logger.info(f"Function {func.__name__} starting")
            result = await func(*args, **kwargs)
            end_time = time.time()
            logger.info(f"Function {func.__name__} finished in {end_time - start_time:.2f} seconds")
            return result
        return async_wrapper
    else:
        # Handle sync functions
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            logger.info(f"Function {func.__name__} starting")
            result = func(*args, **kwargs)
            end_time = time.time()
            logger.info(f"Function {func.__name__} finished in {end_time - start_time:.2f} seconds")
            return result
        return sync_wrapper
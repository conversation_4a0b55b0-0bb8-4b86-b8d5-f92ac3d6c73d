from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from api.request import WebsiteHealthAnalysisRequest, WebsitePresenceRequest
from utils.url_processor import process_urls
from services.reachability_service import check_url_reachability
from services.health_analysis_service import analyze_website_health
from services.presence_analysis_service import analyze_website_presence
from services.database_service import save_complete_analysis, DatabaseService
from services.payload_aggregator import aggregate_website_health_payload
from database.config import get_db, init_db
from utils.logger import get_logger, org_id_var, ref_id_var
import time
import os
from typing import List, Optional
import functools
from api.time_func import timeit

# Initialize FastAPI app with metadata
app = FastAPI(
    title="Website Health Analysis API",
    description="AI-powered website security and usability analysis using Gemini 2.5 Flash",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for production use
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = get_logger(__name__)

@app.middleware("http")
async def dispatch(request: Request, call_next):
    # Extract headers or query parameters
    org_id = request.headers.get("X-Org-ID", "-")
    ref_id = request.headers.get("X-Ref-ID", "-")

    # Set context variables
    org_id_var.set(org_id)
    ref_id_var.set(ref_id)

    response = await call_next(request)
    return response

@app.on_event("startup")
async def startup_event():
    """Initialize database on startup."""
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        # Don't fail startup if database is not available
        logger.warning("Continuing without database initialization")

@app.post("/analyze-website-health")
@timeit
async def analyze_health(request: WebsiteHealthAnalysisRequest, db: Session = Depends(get_db)):
    """
    Analyzes the health of a website by processing its URLs, checking for
    reachability, and performing a health analysis.

    Returns:
        dict: Analysis results with status and detailed findings
    """
    start_time = time.time()
    logger.info(f"Received request for website: {request.website}")
    logger.info(f"Start time: {start_time}")
    # Convert request to dict for database storage
    request_data = {
        "website": request.website,
        "scrapeRequestRefID": request.scrapeRequestRefID,
        "parsed_urls": [{"url_depth": item.url_depth, "urls": item.urls} for item in request.parsed_urls],
        "org_id": request.org_id
    }

    # Validate input
    if not request.parsed_urls:
        logger.warning("No URLs provided in request", request_data)
        raise HTTPException(status_code=400, detail="No URLs provided for analysis")

    try:
        # 1. Process URLs (dedupe, sort, take top 100, convert to dict)
        logger.info("Step 1: Processing URLs...")
        url_dict = process_urls(request.parsed_urls)
        logger.info(f"Processed {len(url_dict)} unique URLs from {sum(len(item.urls) for item in request.parsed_urls)} total URLs")

        if not url_dict:
            logger.warning("No valid URLs found after processing")
            raise HTTPException(status_code=400, detail="No valid URLs found after processing")

        # 2. Check URL reachability (send 15 URLs at once, iterate until 4 reachable)
        logger.info("Step 2: Checking URL reachability...")
        reachable_urls = check_url_reachability(url_dict, target_reachable_count=4)
        logger.info(f"Found {len(reachable_urls)} reachable URLs out of {len(url_dict)} processed")

        if not reachable_urls:
            logger.warning("No reachable URLs found")
            result_data = {
                "status": "warning",
                "message": "No reachable URLs found",
                "processed_urls": len(url_dict),
                "reachable_urls": 0,
                "results": []
            }

            # Generate empty aggregated payload for consistency
            try:
                aggregated_payload = aggregate_website_health_payload(result_data)
                result_data["aggregated_analysis"] = aggregated_payload
            except Exception as agg_error:
                logger.error(f"Failed to generate aggregated payload for empty results: {agg_error}")
                result_data["aggregated_analysis"] = {"error": f"Aggregation failed: {str(agg_error)}"}

            
            # Save to database
            try:
                processing_time = time.time() - start_time
                db_result = save_complete_analysis(request_data, result_data, processing_time)
                result_data["database_info"] = db_result
            except Exception as db_error:
                logger.error(f"Database save failed: {db_error}")
                result_data["database_info"] = {"status": "error", "message": str(db_error)}
            
            return result_data

        # 3. Analyze website health for reachable URLs
        logger.info("Step 3: Analyzing website health...")
        analysis_results = await analyze_website_health(reachable_urls, request.website)
        logger.info(f"Website health analysis complete for {len(analysis_results)} URLs")
        
        # Log analysis results summary
        logger.info(f"=== ANALYSIS RESULTS SUMMARY ===")
        for i, result in enumerate(analysis_results):
            if "error" in result:
                logger.warning(f"URL {i+1} analysis error: {result['error']}")
            else:
                logger.info(f"URL {i+1} analysis completed successfully for: {result.get('analyzed_url', 'Unknown')}")
                # Log key findings
                if 'navigation_issues_exists' in result:
                    logger.info(f"  - Navigation issues: {result.get('navigation_issues_exists', 'N/A')}")
                if 'phishing_site' in result:
                    logger.info(f"  - Phishing site: {result.get('phishing_site', 'N/A')}")
                if 'malware_present' in result:
                    logger.info(f"  - Malware present: {result.get('malware_present', 'N/A')}")
        logger.info(f"=== END ANALYSIS RESULTS SUMMARY ===")

        # Calculate processing time
        processing_time = time.time() - start_time

        result_data = {
            "status": "success",
            "processed_urls": len(url_dict),
            "reachable_urls": len(reachable_urls),
            "analyzed_urls": len(analysis_results),
            "results": analysis_results,
            "processing_time_seconds": round(processing_time, 2)
        }

        # Generate aggregated payload
        try:
            logger.info("Generating aggregated website health payload...")
            aggregated_payload = aggregate_website_health_payload(result_data)
            result_data["aggregated_analysis"] = aggregated_payload
            logger.info("Successfully generated aggregated payload")
        except Exception as agg_error:
            logger.error(f"Failed to generate aggregated payload: {agg_error}")
            result_data["aggregated_analysis"] = {"error": f"Aggregation failed: {str(agg_error)}"}


        # Save to database
        try:
            db_result = save_complete_analysis(request_data, result_data, processing_time)
            result_data["database_info"] = db_result
            logger.info(f"Analysis data saved to database: {db_result}")
        except Exception as db_error:
            logger.error(f"Database save failed: {db_error}")
            result_data["database_info"] = {"status": "error", "message": str(db_error)}
        
        logger.info(f"End time: {time.time()}")
        return result_data

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during analysis: {e}", exc_info=True)
        
        # Save error to database
        error_result = {
            "status": "error",
            "error": str(e),
            "processed_urls": len(url_dict) if 'url_dict' in locals() else 0,
            "reachable_urls": len(reachable_urls) if 'reachable_urls' in locals() else 0,
            "analyzed_urls": len(analysis_results) if 'analysis_results' in locals() else 0,
            "results": []
        }
        
        try:
            processing_time = time.time() - start_time
            db_result = save_complete_analysis(request_data, error_result, processing_time)
            error_result["database_info"] = db_result
        except Exception as db_error:
            logger.error(f"Database save failed for error case: {db_error}")
            error_result["database_info"] = {"status": "error", "message": str(db_error)}
        
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during website analysis: {str(e)}"
        )

@app.get("/analysis-history")
async def get_analysis_history(
    org_id: str = Query(..., description="Organization ID"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """
    Get analysis history for an organization.
    
    Returns:
        dict: List of analysis requests with basic information
    """
    try:
        db_service = DatabaseService(db)
        history = db_service.get_analysis_history(org_id, limit)
        
        return {
            "status": "success",
            "org_id": org_id,
            "count": len(history),
            "history": [
                {
                    "id": req.id,
                    "scrape_request_ref_id": req.scrape_request_ref_id,
                    "website_url": req.website_url,
                    "created_at": req.created_at.isoformat() if req.created_at else None,
                    "updated_at": req.updated_at.isoformat() if req.updated_at else None,
                    "results_count": len(req.analysis_results)
                }
                for req in history
            ]
        }
    except Exception as e:
        logger.error(f"Error retrieving analysis history: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving analysis history: {str(e)}")

@app.get("/analysis-summary/{request_id}")
async def get_analysis_summary(
    request_id: int,
    db: Session = Depends(get_db)
):
    """
    Get detailed summary of an analysis request.
    
    Returns:
        dict: Analysis summary with statistics
    """
    try:
        db_service = DatabaseService(db)
        summary = db_service.get_url_analysis_summary(request_id)
        
        if not summary:
            raise HTTPException(status_code=404, detail="Analysis request not found")
        
        return {
            "status": "success",
            "request_id": request_id,
            "summary": summary
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving analysis summary: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving analysis summary: {str(e)}")

@app.get("/analysis-details/{ref_id}")
async def get_analysis_details(
    ref_id: str,
    db: Session = Depends(get_db)
):
    """
    Get detailed analysis information by reference ID.
    
    Returns:
        dict: Complete analysis details
    """
    try:
        db_service = DatabaseService(db)
        request = db_service.get_analysis_request(ref_id)
        
        if not request:
            raise HTTPException(status_code=404, detail="Analysis request not found")
        
        # Get the latest result
        latest_result = request.analysis_results[-1] if request.analysis_results else None
        
        return {
            "status": "success",
            "request": {
                "id": request.id,
                "scrape_request_ref_id": request.scrape_request_ref_id,
                "website_url": request.website_url,
                "org_id": request.org_id,
                "created_at": request.created_at.isoformat() if request.created_at else None,
                "updated_at": request.updated_at.isoformat() if request.updated_at else None
            },
            "result": {
                "id": latest_result.id if latest_result else None,
                "status": latest_result.status if latest_result else None,
                "processed_urls_count": latest_result.processed_urls_count if latest_result else 0,
                "reachable_urls_count": latest_result.reachable_urls_count if latest_result else 0,
                "analyzed_urls_count": latest_result.analyzed_urls_count if latest_result else 0,
                "processing_time_seconds": latest_result.processing_time_seconds if latest_result else None,
                "created_at": latest_result.created_at.isoformat() if latest_result and latest_result.created_at else None
            } if latest_result else None,
            "url_analyses_count": len(request.url_analyses)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving analysis details: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving analysis details: {str(e)}")
    
@app.get("/health-check")
async def health_check():
    """
    Health check endpoint to verify API is running.
    """
    try:
        # Check if Gemini API key is configured
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        gemini_status = "configured" if gemini_api_key else "missing"
        
        # Check database connectivity
        db_status = "unknown"
        try:
            from database.config import get_db
            db = next(get_db())
            db.execute("SELECT 1")
            db_status = "connected"
        except Exception as db_error:
            logger.warning(f"Database health check failed: {db_error}")
            db_status = "disconnected"
        
        # Check overall health
        overall_status = "healthy" if gemini_status == "configured" and db_status == "connected" else "degraded"
        
        return {
            "status": overall_status,
            "service": "Website Health Analysis API",
            "version": "1.0.0",
            "components": {
                "gemini_api": gemini_status,
                "database": db_status
            },
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": "Website Health Analysis API",
            "version": "1.0.0",
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/analyze-website-presence")
@timeit
async def analyze_presence(request: WebsitePresenceRequest):
    """
    Analyzes the online presence of a website.
    """
    try:
        # Validate request
        if not request.website_url:
            logger.error("Invalid request: missing website_url")
            raise HTTPException(
                status_code=400,
                detail="Invalid request: website_url is required"
            )
        
        logger.info(f"Starting presence analysis for website: {request.website_url}")
        
        # Perform analysis
        analysis_result = await analyze_website_presence(str(request.website_url))
        
        if not analysis_result:
            logger.warning(f"No analysis result returned for {request.website_url}")
            raise HTTPException(
                status_code=500,
                detail="Analysis failed to return results"
            )
        
        logger.info(f"Presence analysis completed successfully for {request.website_url}")
        return analysis_result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during presence analysis: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during website presence analysis: {str(e)}"
        )
import os
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from agents import Agent, Runner
from agents.tool import WebSearchTool

# 1. Set your 40-mini API key
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"


# 2. Define the Agent with web‐search capability
BASE_URL = "https://amazon.in"
SLUGS = {
    "home": "",
    "about_us": "about-us",
    "contact_us": "contact-us",
    "terms_and_conditions": "terms-of-service",
    "privacy_policy": "privacy-policy",
    "refund_policy": "refund-policy",
    "shop": "shop",
    "services": "services",
    "shipping_delivery": "shipping-and-delivery",
    "catalogue": "catalogue"
}

SOCIAL_NETWORKS = [
    "instagram",
    "facebook",
    "youtube",
    "twitter",
    "linkedin",
    "pinterest"
]

# ── Setup HTTP Session with Retries ──────────────────────────────────────────
retry_strategy = Retry(
    total=4,
    backoff_factor=1,
    status_forcelist=[429, 500, 502, 503, 504]
)
adapter = HTTPAdapter(max_retries=retry_strategy)
session = requests.Session()
session.mount("https://", adapter)
session.mount("http://", adapter)

# ── Helper: Fetch WP Page URL by Slug ───────────────────────────────────────
def fetch_wp_url(slug: str) -> str:
    """Return the canonical page URL for a given slug, or 'na' if not found."""
    if not slug:
        return BASE_URL + "/"
    resp = session.get(f"{BASE_URL}/wp-json/wp/v2/pages", params={"slug": slug}, timeout=5)
    resp.raise_for_status()
    items = resp.json()
    return items[0]["link"] if items else "na"

# ── Helper: Fetch Social URL via Agent ──────────────────────────────────────
agent = Agent(
    name="SocialURLFinder",
    model="gpt-4o-mini",
    instructions=(
        "Given a social network name and the business name 'Ecom Empires', "
        "return the official profile URL or 'na' if none exists."
    ),
    tools=[WebSearchTool()]
)

def fetch_social_url(network: str) -> str:
    """Return the official social profile URL, or 'na'."""
    prompt = f"Find the official {network.capitalize()} profile URL for the business Ecom Empires."
    result = Runner.run_sync(agent, prompt)
    url = result.final_output.strip()
    return url if url.startswith("http") else "na"

# ── Main: Aggregate All URLs ─────────────────────────────────────────────────
def gather_all_urls():
    data = {}

    # 1–10: WordPress pages
    for key, slug in SLUGS.items():
        data[key] = fetch_wp_url(slug)

    # 11–16: Social media
    for network in SOCIAL_NETWORKS:
        data[network] = fetch_social_url(network)

    return data

if __name__ == "__main__":
    urls = gather_all_urls()
    for category, url in urls.items():
        print(f"{category}: {url}")    # Now available :contentReference[oaicite:6]{index=6}
